# Skillify: The Developer Assessment Platform

## 1. Introduction

Skillify is a platform designed for creating, managing, and conducting coding assessments and technical tests. It empowers organizations to evaluate candidate skills effectively through a robust API-driven system. Key features include organization management, job posting linkage, customizable tests with various question types, candidate assignment, and results tracking.

## 2. Table of Contents

- [Skillify: The Developer Assessment Platform](#skillify-the-developer-assessment-platform)
  - [1. Introduction](#1-introduction)
  - [2. Table of Contents](#2-table-of-contents)
  - [3. Core Concepts \& Domain Model](#3-core-concepts--domain-model)
    - [3.1. Entities and Relationships](#31-entities-and-relationships)
    - [3.2. User Roles \& Permissions](#32-user-roles--permissions)
  - [4. Architecture Overview](#4-architecture-overview)
    - [4.1. Technology Stack](#41-technology-stack)
    - [4.2. Layered Architecture](#42-layered-architecture)
    - [4.3. Security](#43-security)
    - [4.4. Database \& Migrations](#44-database--migrations)
  - [5. Prerequisites](#5-prerequisites)
  - [6. Quick Start](#6-quick-start)
  - [7. API Documentation](#7-api-documentation)
    - [7.1. Swagger UI](#71-swagger-ui)
    - [7.2. Standard Response Format](#72-standard-response-format)
    - [7.3. Authentication](#73-authentication)
    - [7.4. Organization Management](#74-organization-management)
    - [7.5. Job Management](#75-job-management)
    - [7.6. Test Management](#76-test-management)
    - [7.7. Question Management](#77-question-management)
    - [7.8. Test Assignment Management](#78-test-assignment-management)
    - [7.9. Answer Management](#79-answer-management)
    - [7.10. Authorization Test Endpoints](#710-authorization-test-endpoints)
  - [8. Visualizing Relationships](#8-visualizing-relationships)

## 3. Core Concepts & Domain Model

Skillify revolves around several key entities that model the assessment process:

### 3.1. Entities and Relationships

*   **User**: Represents individuals interacting with the system (admins, organization members, candidates). Authenticated via JWT.
*   **Role & Permission**: System-level roles (e.g., `ROLE_ADMIN`, `ROLE_USER`) with associated global permissions.
*   **Organization**: Represents a company or entity using Skillify. Owns jobs and tests.
    *   An `Organization` has a `createdBy` (User).
    *   An `Organization` has many `OrgMember`s.
    *   An `Organization` has many `Job`s.
*   **OrgMember**: Links a `User` to an `Organization`.
*   **OrganizationRole & OrgPermission**: Roles and permissions specific to an organization (e.g., `ROLE_ORG_ADMIN`, `MANAGE_JOB`).
*   **Job**: Represents a job posting within an `Organization`.
    *   A `Job` belongs to one `Organization`.
    *   A `Job` has `createdBy` and `updatedBy` (Users).
    *   A `Job` can have many `Test`s.
*   **Test**: An assessment designed for a specific `Job`.
    *   A `Test` belongs to one `Job`.
    *   A `Test` has `createdBy` and `updatedBy` (Users).
    *   A `Test` can have many `Question`s.
    *   A `Test` can have many `TestAssignment`s.
*   **Question**: A specific question within a `Test`. Can be of various types (MCQ, Coding, Open-ended).
    *   A `Question` belongs to one `Test`.
    *   Has attributes like `type`, `text`, `options`, `correctAnswer`, `difficulty`, `order`.
*   **Answer**: A candidate's response to a `Question` in a `Test`.
    *   An `Answer` is uniquely identified by a composite key (`testId`, `questionId`, `candidateId`).
    *   Stores the `answer` content (as JSON), `isCorrect` status, and `score`.
*   **TestAssignment**: Assigns a `Test` to a candidate (identified by email).
    *   A `TestAssignment` belongs to one `Test`.
    *   Tracks `status` (PENDING, IN_PROGRESS, COMPLETED), `startTime`, `endTime`, and `score`.

### 3.2. User Roles & Permissions

Skillify employs a dual-permission system:
1.  **Global Roles/Permissions**: Defined at the application level (e.g., `ROLE_ADMIN` having `USER_MANAGE` permission). Managed by `User`, `Role`, `Permission` entities.
2.  **Organizational Roles/Permissions**: Defined within the context of an `Organization` (e.g., a user being `ROLE_ORG_ADMIN` for "Acme Corp" with `MANAGE_JOB` permission for that org). Managed by `OrganizationRole`, `OrgPermission`, and `OrgRoleMapping`. The `OrgPermissionEvaluator` bean is crucial for checking these contextual permissions.

## 4. Architecture Overview

Skillify is built as a Spring Boot application following a layered architecture.

### 4.1. Technology Stack

*   **Backend**: Java, Spring Boot
*   **Data Persistence**: Spring Data JPA, Hibernate
*   **Database**: PostgreSQL
*   **Database Migrations**: Flyway
*   **Security**: Spring Security (JWT for authentication, method-level authorization)
*   **API Documentation**: Springdoc OpenAPI (Swagger UI)
*   **Build Tool**: Maven
*   **Utilities**: Lombok, MapStruct

### 4.2. Layered Architecture

The application follows a standard layered approach for separation of concerns:

1.  **Controllers (`io.skillify.controllers.*`)**:
    *   Handle HTTP requests and responses.
    *   Responsible for input validation (using `@Valid` and DTO validation annotations).
    *   Delegate business logic to Service layer.
    *   Use DTOs for API contracts.
    *   Return `ResponseEntity<ResponseDto<T>>`.
    *   Authorization is often handled here using `@PreAuthorize` and custom permission evaluators (`OrgPermissionEvaluator`).

2.  **Services (`io.skillify.services.*`)**:
    *   Contain the core business logic.
    *   Orchestrate calls to Repositories and other services.
    *   Operate on DTOs for input/output, mapping to/from Entities.
    *   Transactional boundaries are typically managed here (`@Transactional`).

3.  **Repositories (`io.skillify.repositories.*`)**:
    *   Data Access Object (DAO) layer.
    *   Interfaces extending Spring Data JPA repositories (e.g., `JpaRepository`).
    *   Define custom queries using JPQL (`@Query`) or query method naming conventions.

4.  **Mappers (`io.skillify.mappers.*`)**:
    *   Use MapStruct to automate the conversion between DTOs and Entities, reducing boilerplate code.

5.  **Models/Entities (`io.skillify.models.*`)**:
    *   JPA entities representing database tables.
    *   Define relationships, constraints, and auditing information (`@CreatedBy`, `@LastModifiedDate`, etc., handled by `AuditorConfig`).

6.  **DTOs (`io.skillify.dtos.*`)**:
    *   Data Transfer Objects used for API request/response payloads and for transferring data between layers.
    *   Often implemented as Java `record`s for immutability and conciseness.
    *   Include validation annotations (`@NotBlank`, `@Size`, etc.).

7.  **Configuration (`io.skillify.config.*`)**:
    *   Spring configuration classes for security (`SecurityConfig`, `JwtUtil`), OpenAPI (`OpenApiConfig`), auditing (`AuditorConfig`), etc.

8.  **Exceptions (`io.skillify.exceptions.*`)**:
    *   Custom exception classes for specific error conditions (e.g., `ResourceNotFoundException`).
    *   `GlobalExceptionHandler` centralizes exception handling and formats error responses consistently using `ResponseDto`.

### 4.3. Security

*   **Authentication**:
    *   Stateless JWT-based authentication.
    *   Login endpoint (`/api/auth/login`) validates credentials and issues a JWT access token and a refresh token.
    *   JWT access token is stored in an HttpOnly, secure cookie (`JWT`).
    *   Refresh token is stored in an HttpOnly, secure cookie (`JWT_REFRESH`).
    *   `JwtRequestFilter` intercepts requests, validates the JWT from cookies, and sets the `SecurityContext`.
*   **Authorization**:
    *   Method-level security using Spring Security's `@PreAuthorize` annotations.
    *   Combines role-based checks (e.g., `hasRole('ADMIN')`) with custom permission logic via `OrgPermissionEvaluator` for organization-specific permissions (e.g., `@orgPermissionEvaluator.hasPermissionForJob(#jobId, 'MANAGE_JOB')`).
*   **CORS**: Configured in `SecurityConfig` to allow requests from specified origins.
*   **CSRF**: Disabled for stateless API.
*   **Password Storage**: Passwords are hashed using `BCryptPasswordEncoder`.

### 4.4. Database & Migrations

*   **Database**: PostgreSQL is the primary database.
*   **Schema Management**: Flyway is used for version-controlled database migrations. Migration scripts are located in `src/main/resources/db/migration`.
*   **Auditing**: JPA auditing is enabled via `AuditorConfig` to automatically populate `createdBy`, `createdAt`, `updatedBy`, and `updatedAt` fields in entities.

## 5. Prerequisites

Before you begin, ensure you have the following installed:

*   Java JDK
*   Docker (for PostgreSQL, or a standalone PostgreSQL instance)
*   Maven (usually comes with IDEs or can be installed separately; `mvnw` wrapper is provided)

## 6. Quick Start

Follow these steps to get Skillify up and running:

1.  **Clone the repository:**

2.  **Start PostgreSQL using Docker:**
    A convenience script or command:
    ```bash
    docker run --name skillify-db \
      -e POSTGRES_USER=postgres \
      -e POSTGRES_PASSWORD=root \
      -e POSTGRES_DB=skillify \
      -p 5432:5432 \
      -d postgres:latest
    ```
    Alternatively, ensure your PostgreSQL instance is running and accessible.

3.  **Configure Environment Variables (`.env` file):**
    Create a `.env` file in the project root with your database and JWT secret:
    ```properties
    # Development
    POSTGRES_HOST=localhost
    POSTGRES_PORT=5432
    POSTGRES_USER=postgres
    POSTGRES_PASSWORD=root # Change if your Docker/DB password is different
    POSTGRES_DB=skillify
    JWT_SECRET=your_super_strong_and_long_jwt_secret_key_at_least_256_bits # Replace with a strong, secure key
    ```
    The `JWT_SECRET` should be a Base64 encoded string suitable for HMAC-SHA algorithms (e.g., HS256, HS512).

4.  **Configure Flyway (`flyway.conf` - Optional if using default .env values):**
    If your Flyway configuration differs from the default Spring Boot datasource properties, create a `flyway.conf` file in the project root (though typically Spring Boot properties in `application.properties` or `.env` are sufficient):
    ```properties
    flyway.url=*****************************************
    flyway.user=postgres
    flyway.password=root # Change if needed
    flyway.locations=classpath:db/migration
    ```
    Note: Spring Boot typically auto-configures Flyway using `spring.datasource.*` and `spring.flyway.*` properties. Check `application.properties`.

5.  **Build and Run the Application:**
    Using the Maven wrapper:
    ```bash
    ./mvnw clean install
    ./mvnw spring-boot:run
    ```
    The application will start, and Flyway will apply database migrations automatically. It will be available at `http://localhost:8080`.

## 7. API Documentation

### 7.1. Swagger UI

Interactive API documentation is available via Swagger UI once the application is running:

*   **URL:** `http://localhost:8080/swagger-ui/index.html`

You can explore endpoints, view schemas, and test API calls directly from the Swagger UI.

### 7.2. Standard Response Format

All API responses (both success and error) follow a standard structure using `ResponseDto<T>`:

```json
{
  "status": "OK", // Or "CREATED", "BAD_REQUEST", "NOT_FOUND", etc. (HttpStatus enum name)
  "success": true, // boolean indicating success
  "data": { ... }, // The actual payload if success is true (type T)
  "error": null, // Error message string if success is false
  "timestamp": "2025-05-10T14:30:00.123456" // LocalDateTime of the response
}
```

For error responses, `success` will be `false`, `data` will be `null`, and `error` will contain a descriptive message. For example, a 404 Not Found:
```json
{
  "status": "NOT_FOUND",
  "success": false,
  "data": null,
  "error": "Resource not found with id: some-uuid",
  "timestamp": "2025-05-10T14:32:00.789012"
}
```

### 7.3. Authentication

Handles user sign-up, login, logout, and token refresh.

<details>
<summary><b>Register User</b></summary>

*   **Endpoint:** `POST /api/auth/register`
*   **Description:** Creates a new user account.
*   **Request Body Example (`RegisterRequest`):**
    ```json
    {
      "username": "newuser",
      "email": "<EMAIL>",
      "password": "aSecurePassword123!",
      "role": "ROLE_USER" // or "ROLE_ADMIN"
    }
    ```
*   **Success Response (201 CREATED) (`ResponseDto<UserDto>`):**
    ```json
    {
        "status": "CREATED",
        "success": true,
        "data": {
            "id": "a1b2c3d4-e5f6-7890-1234-567890abcdef",
            "email": "<EMAIL>",
            "username": "newuser",
            "lastLogin": "2025-05-10T10:00:00.123456Z", // Or null if first login
            "createdAt": "2025-05-10T10:00:00.123456Z",
            "roles": [
                {
                    "id": 1,
                    "name": "ROLE_USER",
                    "permissions": [
                        {
                            "permissionName": "CREATE_ORG",
                            "description": "Create an organization",
                            "category": "USER"
                        }
                        // ... other permissions for ROLE_USER
                    ]
                }
            ]
        },
        "error": null,
        "timestamp": "2025-05-10T10:00:00.123888"
    }
    ```
</details>

<details>
<summary><b>Login User</b></summary>

*   **Endpoint:** `POST /api/auth/login`
*   **Description:** Authenticates a user, sets JWT and Refresh Token in HttpOnly cookies.
*   **Request Body Example (`LoginRequest`):**
    ```json
    {
      "email": "<EMAIL>",
      "password": "aSecurePassword123!"
    }
    ```
*   **Success Response (200 OK) (`ResponseDto<Map<String, Object>>`):**
    (Cookies `JWT` and `JWT_REFRESH` are set in response headers)
    ```json
    {
        "status": "OK",
        "success": true,
        "data": {
            "refreshToken": "generated-refresh-token-string",
            "accessToken": "generated-jwt-access-token-string",
            "user": {
                "id": "a1b2c3d4-e5f6-7890-1234-567890abcdef",
                "email": "<EMAIL>",
                "username": "newuser",
                "lastLogin": "2025-05-10T10:05:00.456789Z",
                "createdAt": "2025-05-10T10:00:00.123456Z",
                "roles": [ /* as in register response */ ]
            }
        },
        "error": null,
        "timestamp": "2025-05-10T10:05:00.456999"
    }
    ```
</details>

<details>
<summary><b>Logout User</b></summary>

*   **Endpoint:** `POST /api/auth/logout`
*   **Description:** Logs out the authenticated user. Clears JWT and Refresh Token cookies. Requires authentication.
*   **Success Response (200 OK) (`ResponseDto<String>`):**
    ```json
    {
        "status": "OK",
        "success": true,
        "data": "Logged out successfully",
        "error": null,
        "timestamp": "2025-05-10T10:10:00.789012"
    }
    ```
</details>

<details>
<summary><b>Refresh Token</b></summary>

*   **Endpoint:** `POST /api/auth/token/refresh`
*   **Description:** Refreshes the JWT access token using a valid refresh token from cookies. Sets new JWT and Refresh Token cookies.
*   **Success Response (200 OK) (`ResponseDto<String>`):**
    (New `JWT` and `JWT_REFRESH` cookies are set)
    ```json
    {
        "status": "OK",
        "success": true,
        "data": "Token refreshed successfully",
        "error": null,
        "timestamp": "2025-05-10T10:15:00.123456"
    }
    ```
</details>

<details>
<summary><b>Check Credentials Existence</b></summary>

*   **Endpoint:** `POST /api/auth/credentials/check`
*   **Description:** Checks if an email or username already exists.
*   **Request Body Example (`CheckCredentialsRequest`):**
    ```json
    {
      "email": "<EMAIL>",
      "username": "newusername"
    }
    ```
*   **Success Response (200 OK) (`ResponseDto<Map<String, Boolean>>`):**
    ```json
    {
        "status": "OK",
        "success": true,
        "data": {
            "emailValid": false, // false if email exists
            "usernameValid": true  // true if username does not exist
        },
        "error": null,
        "timestamp": "2025-05-10T10:20:00.456789"
    }
    ```
</details>

### 7.4. Organization Management

API for creating and managing organizations and their members.

<details>
<summary><b>Create Organization</b></summary>

*   **Endpoint:** `POST /api/organizations`
*   **Description:** Creates a new organization. The authenticated user becomes the owner with `ROLE_ORG_ADMIN`. Requires authentication.
*   **Request Body Example (`OrgDto.CreateRequest`):**
    ```json
    {
      "name": "Innovatech Solutions"
    }
    ```
*   **Success Response (201 CREATED) (`ResponseDto<OrgDto.Response>`):**
    ```json
    {
      "status": "CREATED",
      "success": true,
      "data": {
        "id": "org-uuid-123",
        "name": "Innovatech Solutions",
        "createdBy": "<EMAIL>", // Email of the creator
        "memberCount": 1
      },
      "error": null,
      "timestamp": "2025-05-10T11:00:00Z"
    }
    ```
</details>

<details>
<summary><b>Get Organization Details</b></summary>

*   **Endpoint:** `GET /api/organizations/{organizationId}`
*   **Description:** Retrieves details for a specific organization. Requires `MANAGE_ORG` permission for the org.
*   **Success Response (200 OK) (`ResponseDto<OrgDto.DetailedResponse>`):**
    ```json
    {
      "status": "OK",
      "success": true,
      "data": {
        "id": "org-uuid-123",
        "name": "Innovatech Solutions",
        "createdBy": "<EMAIL>",
        "members": [
            {
                "userId": "user-uuid-member1",
                "email": "<EMAIL>",
                "roles": ["ROLE_ORG_ADMIN"] // List of OrgRole names
            },
            {
                "userId": "user-uuid-member2",
                "email": "<EMAIL>",
                "roles": ["ROLE_ORG_HR"]
            }
        ]
      },
      "error": null,
      "timestamp": "2025-05-10T11:05:00Z"
    }
    ```
</details>

<details>
<summary><b>Update Organization</b></summary>

*   **Endpoint:** `PATCH /api/organizations/{organizationId}`
*   **Description:** Updates an existing organization's name. Requires `MANAGE_ORG` permission.
*   **Request Body Example (`OrgDto.UpdateRequest`):**
    ```json
    {
      "name": "Innovatech Global"
    }
    ```
*   **Success Response (200 OK) (`ResponseDto<OrgDto.DetailedResponse>`):**
    (Response similar to "Get Organization Details" with updated name)
    ```json
    {
      "status": "OK",
      "success": true,
      "data": {
        "id": "org-uuid-123",
        "name": "Innovatech Global",
        "createdBy": "<EMAIL>",
        "members": [ /* ... member list ... */ ]
      },
      "error": null,
      "timestamp": "2025-05-10T11:10:00Z"
    }
    ```
</details>

<details>
<summary><b>Delete Organization</b></summary>

*   **Endpoint:** `DELETE /api/organizations/{organizationId}`
*   **Description:** Deletes an organization. Requires `MANAGE_ORG` permission.
*   **Success Response (200 OK) (`ResponseDto<String>`):**
    ```json
    {
      "status": "OK",
      "success": true,
      "data": "Organization Innovatech Global deleted", // Or similar message
      "error": null,
      "timestamp": "2025-05-10T11:15:00Z"
    }
    ```
</details>

<details>
<summary><b>Get Current User's Organizations</b></summary>

*   **Endpoint:** `GET /api/organizations/user/current`
*   **Description:** Lists all organizations the authenticated user belongs to. Requires authentication.
*   **Success Response (200 OK) (`ResponseDto<List<OrgDto.Response>>`):**
    ```json
    {
      "status": "OK",
      "success": true,
      "data": [
        {
          "id": "org-uuid-123",
          "name": "Innovatech Global",
          "createdBy": "<EMAIL>",
          "memberCount": 2
        }
        // ... other organizations
      ],
      "error": null,
      "timestamp": "2025-05-10T11:20:00Z"
    }
    ```
</details>

<details>
<summary><b>Add Member to Organization</b></summary>

*   **Endpoint:** `POST /api/organizations/{organizationId}/members`
*   **Description:** Adds a user (by email) to an organization with a specified organizational role. Requires `MANAGE_ORG` permission.
*   **Request Body Example (`OrgDto.AddMemberRequest`):**
    ```json
    {
      "userEmail": "<EMAIL>",
      "roleName": "ROLE_ORG_INTERVIEWER" // e.g., ROLE_ORG_ADMIN, ROLE_ORG_HR
    }
    ```
*   **Success Response (200 OK) (`ResponseDto<String>`):**
    ```json
    {
      "status": "OK",
      "success": true,
      "data": "User <EMAIL> added to organization Innovatech Global with role ROLE_ORG_INTERVIEWER",
      "error": null,
      "timestamp": "2025-05-10T11:25:00Z"
    }
    ```
</details>

<details>
<summary><b>Get Organization Jobs</b></summary>

*   **Endpoint:** `GET /api/organizations/{organizationId}/jobs`
*   **Description:** Retrieves all jobs for a specific organization. Requires `MANAGE_JOB` permission for the org.
*   **Success Response (200 OK) (`ResponseDto<List<JobDto.Response>>`):**
    ```json
    {
      "status": "OK",
      "success": true,
      "data": [
        {
          "id": "job-uuid-abc",
          "title": "Software Engineer",
          "description": "Develop cool stuff.",
          "organizationId": "org-uuid-123",
          "isActive": true,
          "createdBy": "<EMAIL>",
          "updatedBy": "<EMAIL>",
          "createdAt": "2025-02-01T10:00:00Z",
          "updatedAt": "2025-02-01T10:00:00Z"
        }
      ],
      "error": null,
      "timestamp": "2025-05-10T11:30:00Z"
    }
    ```
</details>

<details>
<summary><b>Get All Tests of an Organization (Paginated)</b></summary>

*   **Endpoint:** `GET /api/organizations/{organizationId}/tests`
*   **Description:** Retrieves a paginated list of tests for an organization. Query params: `page` (0-indexed), `size`. Requires `MANAGE_ORG` permission (or perhaps `VIEW_ORG_TESTS`).
*   **Example URL:** `/api/organizations/org-uuid-123/tests?page=0&size=10`
*   **Success Response (200 OK) (`ResponseDto<Page<TestDto.Response>>`):**
    ```json
    {
      "status": "OK",
      "success": true,
      "data": {
        "content": [
          {
            "id": "test-uuid-xyz",
            "name": "Java Basic Test",
            "description": "Basic Java assessment.",
            "timeLimit": 60,
            "jobId": "job-uuid-abc",
            "jobTitle": "Software Engineer",
            "startTime": "2025-06-01T09:00:00Z",
            "endTime": "2025-06-30T18:00:00Z",
            "status": "PUBLISHED",
            "createdBy": "<EMAIL>",
            "updatedBy": "<EMAIL>",
            "createdAt": "2025-05-15T10:00:00Z",
            "updatedAt": "2025-05-16T11:00:00Z"
          }
        ],
        "pageable": {
          "sort": { "sorted": false, "unsorted": true, "empty": true }, // Example sort
          "offset": 0,
          "pageNumber": 0,
          "pageSize": 10,
          "paged": true,
          "unpaged": false
        },
        "totalPages": 1,
        "totalElements": 1,
        "last": true,
        "size": 10,
        "number": 0,
        "sort": { "sorted": false, "unsorted": true, "empty": true },
        "numberOfElements": 1,
        "first": true,
        "empty": false
      },
      "error": null,
      "timestamp": "2025-05-10T11:35:00Z"
    }
    ```
</details>

### 7.5. Job Management

Manage job postings associated with organizations.

<details>
<summary><b>Create Job for an Organization</b></summary>

*   **Endpoint:** `POST /api/organizations/{organizationId}/jobs`
*   **Description:** Creates a new job posting for the specified organization. Requires `MANAGE_JOB` permission for the organization.
*   **Request Body Example (`JobDto.CreateRequest`):**
    ```json
    {
      "title": "Senior Backend Developer",
      "description": "Looking for an experienced backend dev."
    }
    ```
*   **Success Response (201 CREATED) (`ResponseDto<JobDto.Response>`):**
    ```json
    {
      "status": "CREATED",
      "success": true,
      "data": {
        "id": "job-uuid-def",
        "title": "Senior Backend Developer",
        "description": "Looking for an experienced backend dev.",
        "organizationId": "org-uuid-123",
        "isActive": true,
        "createdBy": "<EMAIL>",
        "updatedBy": "<EMAIL>",
        "createdAt": "2025-05-10T12:00:00Z",
        "updatedAt": "2025-05-10T12:00:00Z"
      },
      "error": null,
      "timestamp": "2025-05-10T12:00:00.123Z"
    }
    ```
</details>

<details>
<summary><b>Get Job Details</b></summary>

*   **Endpoint:** `GET /api/jobs/{jobId}`
*   **Description:** Retrieves details for a specific job. Requires `MANAGE_JOB` permission for the job's organization.
*   **Success Response (200 OK) (`ResponseDto<JobDto.Response>`):**
    ```json
    {
      "status": "OK",
      "success": true,
      "data": {
        "id": "job-uuid-def",
        "title": "Senior Backend Developer",
        "description": "Looking for an experienced backend dev.",
        "organizationId": "org-uuid-123",
        "isActive": true,
        "createdBy": "<EMAIL>",
        "updatedBy": "<EMAIL>",
        "createdAt": "2025-05-10T12:00:00Z",
        "updatedAt": "2025-05-10T12:05:00Z"
      },
      "error": null,
      "timestamp": "2025-05-10T12:05:00.456Z"
    }
    ```
</details>

<details>
<summary><b>Update Job</b></summary>

*   **Endpoint:** `PATCH /api/jobs/{jobId}`
*   **Description:** Updates an existing job posting. Requires `MANAGE_JOB` permission for the job's organization.
*   **Request Body Example (`JobDto.UpdateRequest`):**
    ```json
    {
      "title": "Lead Backend Developer",
      "description": "Lead the development team.",
      "isActive": false
    }
    ```
*   **Success Response (200 OK) (`ResponseDto<JobDto.Response>`):**
    (Response similar to "Get Job Details" with updated fields)
    ```json
    {
      "status": "OK",
      "success": true,
      "data": {
        "id": "job-uuid-def",
        "title": "Lead Backend Developer",
        "description": "Lead the development team.",
        "organizationId": "org-uuid-123",
        "isActive": false,
        "createdBy": "<EMAIL>",
        "updatedBy": "<EMAIL>",
        "createdAt": "2025-05-10T12:00:00Z",
        "updatedAt": "2025-05-10T12:10:00Z"
      },
      "error": null,
      "timestamp": "2025-05-10T12:10:00.789Z"
    }
    ```
</details>

<details>
<summary><b>Delete Job</b></summary>

*   **Endpoint:** `DELETE /api/jobs/{jobId}`
*   **Description:** Deletes a job posting. Requires `MANAGE_JOB` permission for the job's organization.
*   **Success Response (200 OK) (`ResponseDto<String>`):**
    ```json
    {
      "status": "OK",
      "success": true,
      "data": "Job deleted successfully",
      "error": null,
      "timestamp": "2025-05-10T12:15:00.123Z"
    }
    ```
</details>

### 7.6. Test Management

APIs for creating, managing, and retrieving tests associated with jobs.

<details>
<summary><b>Create Test for a Job</b></summary>

*   **Endpoint:** `POST /api/jobs/{jobId}/tests`
*   **Description:** Creates a new test for a specific job. Requires `MANAGE_TEST` permission for the job's organization.
*   **Request Body Example (`TestDto.CreateRequest`):**
    ```json
    {
      "name": "Java Coding Challenge",
      "description": "A test to assess Java coding skills.",
      "timeLimit": 90,
      "startTime": "2025-06-01T09:00:00Z",
      "endTime": "2025-06-30T18:00:00Z"
    }
    ```
*   **Success Response (201 CREATED) (`ResponseDto<TestDto.Response>`):**
    ```json
    {
      "status": "CREATED",
      "success": true,
      "data": {
        "id": "test-uuid-123",
        "name": "Java Coding Challenge",
        "description": "A test to assess Java coding skills.",
        "timeLimit": 90,
        "jobId": "job-uuid-def",
        "jobTitle": "Senior Backend Developer",
        "startTime": "2025-06-01T09:00:00Z",
        "endTime": "2025-06-30T18:00:00Z",
        "status": "DRAFT", // Default status
        "createdBy": "<EMAIL>",
        "updatedBy": "<EMAIL>",
        "createdAt": "2025-05-10T13:00:00Z",
        "updatedAt": "2025-05-10T13:00:00Z"
      },
      "error": null,
      "timestamp": "2025-05-10T13:00:00.123Z"
    }
    ```
</details>

<details>
<summary><b>Get Test by ID</b></summary>

*   **Endpoint:** `GET /api/tests/{testId}`
*   **Description:** Retrieves details of a specific test. Requires `MANAGE_TEST` permission for the test's organization.
*   **Success Response (200 OK) (`ResponseDto<TestDto.Response>`):**
    (Similar structure to "Create Test" response, showing current state)
    ```json
    {
      "status": "OK",
      "success": true,
      "data": {
        "id": "test-uuid-123",
        "name": "Java Coding Challenge",
        "description": "A test to assess Java coding skills.",
        "timeLimit": 90,
        "jobId": "job-uuid-def",
        "jobTitle": "Senior Backend Developer",
        "startTime": "2025-06-01T09:00:00Z",
        "endTime": "2025-06-30T18:00:00Z",
        "status": "PUBLISHED",
        "createdBy": "<EMAIL>",
        "updatedBy": "<EMAIL>",
        "createdAt": "2025-05-10T13:00:00Z",
        "updatedAt": "2025-05-10T13:05:00Z"
      },
      "error": null,
      "timestamp": "2025-05-10T13:05:00.456Z"
    }
    ```
</details>

<details>
<summary><b>Update Test</b></summary>

*   **Endpoint:** `PATCH /api/tests/{testId}`
*   **Description:** Updates an existing test. Requires `MANAGE_TEST` permission for the test's organization.
*   **Request Body Example (`TestDto.UpdateRequest`):**
    ```json
    {
      "name": "Advanced Java Coding Challenge",
      "timeLimit": 120,
      "status": "PUBLISHED"
    }
    ```
*   **Success Response (200 OK) (`ResponseDto<TestDto.Response>`):**
    (Similar to "Get Test by ID" response, reflecting updated fields)
</details>

<details>
<summary><b>Delete Test</b></summary>

*   **Endpoint:** `DELETE /api/tests/{testId}`
*   **Description:** Deletes a test. Requires `MANAGE_TEST` permission for the test's organization.
*   **Success Response (200 OK) (`ResponseDto<Void>` or `ResponseDto<String>`):**
    ```json
    {
      "status": "OK",
      "success": true,
      "data": null, // Or "Test deleted successfully"
      "error": null,
      "timestamp": "2025-05-10T13:15:00Z"
    }
    ```
</details>

<details>
<summary><b>Get Tests by Job ID</b></summary>

*   **Endpoint:** `GET /api/jobs/{jobId}/tests`
*   **Description:** Retrieves all tests for a specific job. Requires `MANAGE_TEST` permission for the job's organization.
*   **Success Response (200 OK) (`ResponseDto<List<TestDto.Response>>`):**
    (A list of test response objects)
</details>

<details>
<summary><b>Get Tests by Job ID and Status</b></summary>

*   **Endpoint:** `GET /api/jobs/{jobId}/tests/status?status={status}`
*   **Description:** Retrieves tests for a job, filtered by status (`DRAFT`, `PUBLISHED`, `ARCHIVED`). Requires `MANAGE_TEST` permission.
*   **Example URL:** `/api/jobs/job-uuid-def/tests/status?status=PUBLISHED`
*   **Success Response (200 OK) (`ResponseDto<List<TestDto.Response>>`):**
    (A list of test response objects matching the status)
</details>

<details>
<summary><b>Count Tests by Job ID</b></summary>

*   **Endpoint:** `GET /api/jobs/{jobId}/tests/count`
*   **Description:** Counts tests for a specific job. Requires `MANAGE_TEST` permission.
*   **Success Response (200 OK) (`ResponseDto<Long>`):**
    ```json
    {
      "status": "OK",
      "success": true,
      "data": 5, // The count
      "error": null,
      "timestamp": "2025-05-10T13:30:00Z"
    }
    ```
</details>

<details>
<summary><b>Get All Tests (Admin)</b></summary>

*   **Endpoint:** `GET /api/tests`
*   **Description:** Retrieves all tests in the system. Requires `ROLE_ADMIN`.
*   **Success Response (200 OK) (`ResponseDto<List<TestDto.Response>>`):**
    (A list of all test response objects in the system)
</details>

<details>
<summary><b>Get Tests by Status (Admin)</b></summary>

*   **Endpoint:** `GET /api/tests/status/{status}`
*   **Description:** Retrieves all tests with a specific status. Requires `ROLE_ADMIN`.
*   **Example URL:** `/api/tests/status/PUBLISHED`
*   **Success Response (200 OK) (`ResponseDto<List<TestDto.Response>>`):**
    (A list of test response objects matching the status)
</details>

<details>
<summary><b>Get Test Results</b></summary>

*   **Endpoint:** `GET /api/tests/{testId}/results`
*   **Description:** Retrieves results for a test, including candidates and their answers. Optional query params: `candidateId`, `questionId`. Requires `MANAGE_TEST` permission for the test's organization.
*   **Success Response (200 OK) (`ResponseDto<AnswerDto.TestAnswersResponse>`):**
    ```json
    {
      "status": "OK",
      "success": true,
      "data": {
        "testId": "test-uuid-123",
        "candidateAnswers": [
          {
            "candidateId": "candidate-uuid-abc",
            "answers": [
              {
                "questionId": "question-uuid-q1",
                "answer": { "selectedOption": "C" }, // Example for MCQ
                "isCorrect": true,
                "score": 1.0, // Or some other score value
                "questionOrder": 1
              },
              {
                "questionId": "question-uuid-q2",
                "answer": { "code": "public class Solution {}" }, // Example for CODING
                "isCorrect": null, // If not auto-gradable
                "score": null,
                "questionOrder": 2
              }
            ]
          }
          // ... other candidates
        ]
      },
      "error": null,
      "timestamp": "2025-05-10T13:45:00Z"
    }
    ```
</details>

### 7.7. Question Management

APIs for managing questions within tests.

<details>
<summary><b>Create Question for a Test</b></summary>

*   **Endpoint:** `POST /api/tests/{testId}/questions`
*   **Description:** Adds a new question to a test. Requires `MANAGE_TEST` permission for the test's organization.
*   **Request Body Example (`QuestionDto.CreateQuestionRequest`):**
    ```json
    {
      "type": "MCQ", // Or "OPEN_ENDED", "CODING"
      "text": "What is the capital of France?",
      "options": { "A": "Berlin", "B": "Madrid", "C": "Paris", "D": "Rome" }, // For MCQ
      "correctAnswer": { "value": "C" }, // For single-select MCQ
      // For multi-select MCQ: "correctAnswer": { "values": ["A", "C"] }
      // For OPEN_ENDED/CODING, `correctAnswer` might be structured differently or be null for manual grading.
      "difficulty": "EASY", // Or "MEDIUM", "HARD"
      "order": 1 // Optional, will auto-assign if null
    }
    ```
*   **Success Response (201 CREATED) (`ResponseDto<QuestionDto.Response>`):**
    ```json
    {
      "status": "CREATED",
      "success": true,
      "data": {
        "id": "question-uuid-new",
        "testId": "test-uuid-123",
        "type": "MCQ",
        "text": "What is the capital of France?",
        "options": { "A": "Berlin", "B": "Madrid", "C": "Paris", "D": "Rome" },
        "correctAnswer": { "value": "C" },
        "difficulty": "EASY",
        "order": 1,
        "createdBy": "<EMAIL>",
        "updatedBy": "<EMAIL>",
        "createdAt": "2025-05-10T14:00:00Z",
        "updatedAt": "2025-05-10T14:00:00Z"
      },
      "error": null,
      "timestamp": "2025-05-10T14:00:00.123Z"
    }
    ```
</details>

<details>
<summary><b>Get Questions for a Test</b></summary>

*   **Endpoint:** `GET /api/tests/{testId}/questions`
*   **Description:** Retrieves questions for a test. Query params: `metric` (e.g., "count"), `difficulty`, `sort` (e.g., "ordered"), `type`. Requires `MANAGE_TEST` permission.
*   **Example URL:** `/api/tests/test-uuid-123/questions?difficulty=EASY&sort=ordered`
*   **Success Response (200 OK) (`ResponseDto<List<QuestionDto.Response>>` or `ResponseDto<Long>` if metric=count):**
    (List of question response objects, or a count)
</details>

<details>
<summary><b>Get Specific Question from a Test</b></summary>

*   **Endpoint:** `GET /api/tests/{testId}/questions/{questionId}`
*   **Description:** Retrieves a specific question. Requires `MANAGE_TEST` permission.
*   **Success Response (200 OK) (`ResponseDto<QuestionDto.Response>`):**
    (Single question response object)
</details>

<details>
<summary><b>Update Question in a Test</b></summary>

*   **Endpoint:** `PATCH /api/tests/{testId}/questions/{questionId}`
*   **Description:** Updates an existing question. Requires `MANAGE_TEST` permission.
*   **Request Body Example (`QuestionDto.BaseQuestion` - partial updates allowed):**
    ```json
    {
      "text": "What is the primary color code for blue in RGB?",
      "difficulty": "MEDIUM",
      "correctAnswer": { "value": "0000FF" } // Example for a text-based answer
    }
    ```
*   **Success Response (200 OK) (`ResponseDto<QuestionDto.Response>`):**
    (Updated question response object)
</details>

<details>
<summary><b>Delete Question from a Test</b></summary>

*   **Endpoint:** `DELETE /api/tests/{testId}/questions/{questionId}`
*   **Description:** Deletes a question. Requires `MANAGE_TEST` permission.
*   **Success Response (200 OK) (`ResponseDto<Void>` or `ResponseDto<String>`):**
    ```json
    {
      "status": "OK",
      "success": true,
      "data": null, // Or "Question deleted successfully"
      "error": null,
      "timestamp": "2025-05-10T14:20:00Z"
    }
    ```
</details>

<details>
<summary><b>Batch Create Questions for a Test</b></summary>

*   **Endpoint:** `POST /api/tests/{testId}/questions/batch`
*   **Description:** Creates multiple questions for a test in one operation. Requires `MANAGE_TEST` permission.
*   **Request Body Example (`QuestionDto.BatchCreateQuestionsRequest`):**
    ```json
    {
      "questions": [
        {
          "type": "OPEN_ENDED", "text": "Explain OOP.", "difficulty": "MEDIUM", "order": 2
        },
        {
          "type": "CODING", "text": "Write a function to reverse a string.", "difficulty": "MEDIUM", "order": 3
        }
      ]
    }
    ```
*   **Success Response (201 CREATED) (`ResponseDto<QuestionDto.BatchCreateQuestionsResponse>`):**
    ```json
    {
      "status": "CREATED",
      "success": true,
      "data": {
        "testId": "test-uuid-123",
        "createdQuestions": 2,
        "metadata": {
            "createdAt": "2025-05-10T14:25:00Z",
            "updatedAt": "2025-05-10T14:25:00Z",
            "createdBy": "<EMAIL>",
            "updatedBy": "<EMAIL>"
        },
        "questions": [
          { "id": "question-uuid-q2", "type": "OPEN_ENDED", "text": "Explain OOP.", "difficulty": "MEDIUM", "order": 2, /* ... */ },
          { "id": "question-uuid-q3", "type": "CODING", "text": "Write a function to reverse a string.", "difficulty": "MEDIUM", "order": 3, /* ... */ }
        ]
      },
      "error": null,
      "timestamp": "2025-05-10T14:25:00.123Z"
    }
    ```
</details>

<details>
<summary><b>Batch Update Questions for a Test</b></summary>

*   **Endpoint:** `PATCH /api/tests/{testId}/questions/batch`
*   **Description:** Updates, creates, or deletes multiple questions. `id: null` creates. `isDeleted: true` deletes. Requires `MANAGE_TEST` permission.
*   **Request Body Example (`QuestionDto.BatchUpdateQuestionsRequest`):**
    ```json
    {
      "questions": [
        { "id": "question-uuid-q1", "text": "Updated: What is 2+2?", "difficulty": "EASY" }, // Update
        { "type": "MCQ", "text": "New Q?", /*...*/ "difficulty": "EASY", "order": 4 }, // Create (id is null)
        { "id": "question-uuid-to-delete", "isDeleted": true } // Delete
      ]
    }
    ```
*   **Success Response (200 OK) (`ResponseDto<QuestionDto.BatchUpdateQuestionsResponse>`):**
    ```json
    {
      "status": "OK",
      "success": true,
      "data": {
        "testId": "test-uuid-123",
        "createdCount": 1,
        "updatedCount": 1,
        "deletedCount": 1,
        "resultingQuestions": [ /* list of QuestionDto.Response for created/updated questions */ ]
      },
      "error": null,
      "timestamp": "2025-05-10T14:30:00Z"
    }
    ```
</details>

<details>
<summary><b>Batch Delete Questions from a Test</b></summary>

*   **Endpoint:** `DELETE /api/tests/{testId}/questions/batch`
*   **Description:** Deletes multiple questions from a test using their IDs. Requires `MANAGE_TEST` permission.
*   **Request Body Example (`QuestionDto.BatchDeleteQuestionsRequest`):**
    ```json
    {
      "questionIds": ["question-uuid-q2", "question-uuid-q3"]
    }
    ```
*   **Success Response (200 OK) (`ResponseDto<QuestionDto.BatchDeleteQuestionsResponse>`):**
    ```json
    {
      "status": "OK",
      "success": true,
      "data": {
         "testId": "test-uuid-123",
         "deletedQuestions": 2,
         "failedIds": [] // List of UUIDs that were not found or failed to delete
      },
      "error": null,
      "timestamp": "2025-05-10T14:35:00Z"
    }
    ```
</details>

### 7.8. Test Assignment Management

APIs for assigning tests to candidates and managing these assignments.

<details>
<summary><b>Create Batch of Test Assignments</b></summary>

*   **Endpoint:** `POST /api/test-assignments`
*   **Description:** Assigns a test to multiple candidates via email. Requires `MANAGE_TEST` permission for the test's organization.
*   **Request Body Example (`TestAssignmentDto.CreateTestAssignmentRequest`):**
    ```json
    {
      "testId": "test-uuid-123",
      "assignments": [
        { "email": "<EMAIL>" },
        { "email": "<EMAIL>" }
      ]
    }
    ```
*   **Success Response (201 CREATED) (`ResponseDto<List<TestAssignmentDto.Response>>`):**
    ```json
    {
      "status": "CREATED",
      "success": true,
      "data": [
        {
          "id": "assign-uuid-c1",
          "testId": "test-uuid-123",
          "candidateEmail": "<EMAIL>",
          "status": "PENDING",
          "startTime": null,
          "endTime": null,
          "score": null,
          "createdBy": "<EMAIL>",
          "updatedBy": "<EMAIL>",
          "createdAt": "2025-05-10T15:00:00Z",
          "updatedAt": "2025-05-10T15:00:00Z"
        }
        // ... other assignment responses
      ],
      "error": null,
      "timestamp": "2025-05-10T15:00:00.123Z"
    }
    ```
</details>

<details>
<summary><b>Get Test Assignment by ID</b></summary>

*   **Endpoint:** `GET /api/test-assignments/{assignmentId}`
*   **Description:** Retrieves a specific test assignment. Requires `MANAGE_TEST` for the assignment's test org, or if the user is an Admin.
*   **Success Response (200 OK) (`ResponseDto<TestAssignmentDto.Response>`):**
    (Single TestAssignmentDto.Response object)
</details>

<details>
<summary><b>Get Test Assignments by Status (Admin)</b></summary>

*   **Endpoint:** `GET /api/test-assignments?status={status}`
*   **Description:** Retrieves all test assignments with a specific status (`PENDING`, `IN_PROGRESS`, `COMPLETED`). Requires `ROLE_ADMIN`.
*   **Success Response (200 OK) (`ResponseDto<List<TestAssignmentDto.Response>>`):**
    (List of TestAssignmentDto.Response objects)
</details>

<details>
<summary><b>Update Test Assignment</b></summary>

*   **Endpoint:** `PATCH /api/test-assignments/{assignmentId}`
*   **Description:** Updates an assignment's status. Requires `MANAGE_TEST` for the assignment's test org.
*   **Request Body Example (`TestAssignmentDto.UpdateTestAssignmentRequest`):**
    ```json
    {
      "status": "IN_PROGRESS" // Or "COMPLETED", "PENDING"
    }
    ```
*   **Success Response (200 OK) (`ResponseDto<TestAssignmentDto.Response>`):**
    (Updated TestAssignmentDto.Response object. `startTime` or `endTime` might be set based on status change.)
</details>

<details>
<summary><b>Delete Test Assignment</b></summary>

*   **Endpoint:** `DELETE /api/test-assignments/{assignmentId}`
*   **Description:** Deletes a test assignment. Requires `MANAGE_TEST` for the assignment's test org.
*   **Success Response (200 OK) (`ResponseDto<Void>` or `ResponseDto<String>`):**
    ```json
    {
      "status": "OK",
      "success": true,
      "data": null, // Or "Test assignment deleted successfully"
      "error": null,
      "timestamp": "2025-05-10T15:20:00Z"
    }
    ```
</details>

<details>
<summary><b>Get Assignments for a Specific Test</b></summary>

*   **Endpoint:** `GET /api/tests/{testId}/test-assignments`
*   **Description:** Gets assignments for a test. Query params: `status`, `metric` (e.g., "count"). Requires `MANAGE_TEST` for the test's organization.
*   **Example URL:** `/api/tests/test-uuid-123/test-assignments?status=COMPLETED`
*   **Success Response (200 OK) (`ResponseDto<List<TestAssignmentDto.Response>>` or `ResponseDto<Long>` if metric=count):**
    (List of assignments or a count)
</details>

### 7.9. Answer Management

APIs for candidates submitting answers and for retrieving/managing them.

<details>
<summary><b>Submit an Answer</b></summary>

*   **Endpoint:** `POST /api/answers`
*   **Description:** Submits an answer for a question in a test by the authenticated candidate. Requires the candidate to be assigned to the test (`@orgPermissionEvaluator.canTakeTest`).
*   **Request Body Example (`AnswerDto.SubmitAnswerRequest`):**
    ```json
    {
      "testId": "test-uuid-123",
      "questionId": "question-uuid-q1",
      "answer": { "selectedOption": "C" } // Example for MCQ
      // For coding: "answer": { "code": "...", "language": "java" }
      // For open-ended: "answer": { "text": "My detailed explanation..." }
    }
    ```
*   **Success Response (201 CREATED) (`ResponseDto<AnswerDto.Response>`):**
    ```json
    {
      "status": "CREATED",
      "success": true,
      "data": {
        "testId": "test-uuid-123",
        "questionId": "question-uuid-q1",
        "candidateId": "current-candidate-uuid",
        "answer": { "selectedOption": "C" },
        "isCorrect": true, // If auto-gradable and correct
        "score": 1.0 // Score from 0.0 to 1.0 for MCQ, or other grading logic
      },
      "error": null,
      "timestamp": "2025-05-10T16:00:00Z"
    }
    ```
</details>

<details>
<summary><b>Submit Multiple Answers (Batch)</b></summary>

*   **Endpoint:** `POST /api/answers/batch`
*   **Description:** Submits multiple answers for a test. Requires candidate to be assigned to the test.
*   **Request Body Example (`AnswerDto.BatchSubmitAnswerRequest`):**
    ```json
    {
      "testId": "test-uuid-123",
      "answers": [
        { "questionId": "question-uuid-q1", "answer": { "selectedOption": "C" } },
        { "questionId": "question-uuid-q2", "answer": { "textResponse": "OOP is..." } }
      ]
    }
    ```
*   **Success Response (201 CREATED) (`ResponseDto<AnswerDto.BatchSubmissionResponse>`):**
    ```json
    {
      "status": "CREATED",
      "success": true,
      "data": {
        "testId": "test-uuid-123",
        "candidateId": "current-candidate-uuid",
        "answeredQuestions": 2, // Number of answers successfully processed in this batch
        // "totalQuestions": 10,
        "averageScore": 0.75, // Example average score for processed answers
        "correctAnswers": 1, // Number of perfectly correct answers processed
        "answerDetails": [
          { "testId": "test-uuid-123", "questionId": "question-uuid-q1", "candidateId": "...", "answer": { "...": "..." }, "isCorrect": true, "score": 1.0 },
          { "testId": "test-uuid-123", "questionId": "question-uuid-q2", "candidateId": "...", "answer": { "...": "..." }, "isCorrect": false, "score": 0.5 }
        ]
      },
      "error": null,
      "timestamp": "2025-05-10T16:05:00Z"
    }
    ```
</details>

<details>
<summary><b>Get Specific Answer (Test, Question, Candidate)</b></summary>

*   **Endpoint:** `GET /api/tests/{testId}/questions/{questionId}/candidates/{candidateId}/answers`
*   **Description:** Retrieves a specific answer. Requires `MANAGE_TEST` permission for the test's organization.
*   **Success Response (200 OK) (`ResponseDto<AnswerDto.Response>`):**
    (Single `AnswerDto.Response` object)
</details>

<details>
<summary><b>Delete Specific Answer</b></summary>

*   **Endpoint:** `DELETE /api/tests/{testId}/questions/{questionId}/candidates/{candidateId}/answers`
*   **Description:** Deletes a specific answer. Requires `MANAGE_TEST` permission for the test's organization.
*   **Success Response (200 OK) (`ResponseDto<Void>` or `ResponseDto<String>`):**
    ```json
    {
      "status": "OK",
      "success": true,
      "data": null, // Or "Answer deleted successfully"
      "error": null,
      "timestamp": "2025-05-10T16:15:00Z"
    }
    ```
</details>

<details>
<summary><b>Get Answers for a Test and Candidate</b></summary>

*   **Endpoint:** `GET /api/tests/{testId}/candidates/{candidateId}/answers`
*   **Description:** Retrieves answers for a specific test and candidate. Optional `metric` query param (`average-score`, `correct-count`, `total-count`). Requires `MANAGE_TEST` permission.
*   **Success Response (200 OK) (`ResponseDto<List<AnswerDto.Response>>` or `ResponseDto<Float/Long>` if metric is used):**
    *   If no metric: List of `AnswerDto.Response`.
    *   If `metric=average-score`: `data` contains the average score (Float).
    *   If `metric=correct-count`: `data` contains the count of correct answers (Long).
    *   If `metric=total-count`: `data` contains the total count of answers (Long).
</details>

### 7.10. Authorization Test Endpoints

Endpoints for verifying authorization rules.

<details>
<summary><b>Public Endpoint</b></summary>

*   **Endpoint:** `GET /api/test/public`
*   **Description:** Accessible to all, no authentication required.
*   **Success Response (200 OK) (String):**
    ```text
    This endpoint is available for everyone, even unauthenticated users.
    ```
</details>

<details>
<summary><b>Open Endpoint (Authenticated Users)</b></summary>

*   **Endpoint:** `GET /api/test/open`
*   **Description:** Accessible only to authenticated users.
*   **Success Response (200 OK) (String):**
    ```text
    This endpoint is available for authenticated users.
    ```
</details>

<details>
<summary><b>Admin Endpoint</b></summary>

*   **Endpoint:** `GET /api/test/admin`
*   **Description:** Accessible only to users with `ROLE_ADMIN`.
*   **Success Response (200 OK) (String):**
    ```text
    Hello Admin! You have access to admin resources.
    ```
</details>

## 8. Visualizing Relationships

```mermaid
erDiagram

    USER {
        UUID id PK
        string email
        string username
        boolean verified
        timestamp createdAt
        timestamp updatedAt
    }

    ROLE {
        bigint id PK
        string name "e.g., ROLE_USER, ROLE_ADMIN"
    }

    PERMISSION {
        bigint id PK
        string permissionName
        string description
        string category
    }

    USER }o--o{ ROLE : "has_global_roles (user_roles)"
    ROLE }o--o{ PERMISSION : "has_global_permissions (role_permissions)"

    REFRESH_TOKEN {
        bigint id PK
        string token
        timestamp expiryDate
        UUID userId FK
    }
    USER ||--o{ REFRESH_TOKEN : "owns"


    ORGANIZATION {
        UUID id PK
        string name
        UUID createdBy FK "User who created org"
        timestamp createdAt
    }
    USER              ||--o{ ORGANIZATION : "created_by"


    ORG_MEMBER {
        UUID organizationId FK "Part of composite PK"
        UUID userId FK "Part of composite PK"
    }
    ORGANIZATION      ||--o{ ORG_MEMBER : "has_members"
    USER              ||--o{ ORG_MEMBER : "is_member_of"


    ORGANIZATION_ROLE {
        bigint id PK
        string name "e.g., ROLE_ORG_ADMIN"
    }

    ORG_PERMISSION {
        bigint id PK
        string name "e.g., MANAGE_JOB"
        string description
        string category
    }
    ORGANIZATION_ROLE }o--o{ ORG_PERMISSION : "has_org_permissions (org_role_permissions)"


    ORG_ROLE_MAPPING {
        UUID organizationId FK "Part of composite PK"
        UUID userId FK "Part of composite PK"
        bigint roleId FK "References ORGANIZATION_ROLE(id)"
    }
    ORGANIZATION      ||--o{ ORG_ROLE_MAPPING : "defines_context_for"
    USER              ||--o{ ORG_ROLE_MAPPING : "has_role_in_org"
    ORGANIZATION_ROLE ||--o{ ORG_ROLE_MAPPING : "is_assigned_to_user_in_org"


    JOB {
        UUID id PK
        string title
        string description
        boolean isActive
        UUID organizationId FK
        UUID createdBy FK
        UUID updatedBy FK
        timestamp createdAt
        timestamp updatedAt
    }
    ORGANIZATION      ||--o{ JOB : "posts"
    USER              ||--o{ JOB : "created_job"
    USER              ||--o{ JOB : "updated_job"


    TEST {
        UUID id PK
        string name
        string description
        integer timeLimit "in minutes"
        string status "DRAFT, PUBLISHED, ARCHIVED"
        timestamp startTime
        timestamp endTime
        UUID jobId FK
        UUID createdBy FK
        UUID updatedBy FK
        timestamp createdAt
        timestamp updatedAt
    }
    JOB               ||--o{ TEST : "includes"
    USER              ||--o{ TEST : "created_test"
    USER              ||--o{ TEST : "updated_test"


    QUESTION {
        UUID id PK
        string type "MCQ, OPEN_ENDED, CODING"
        string text
        jsonb options "For MCQ"
        jsonb correctAnswer
        string difficulty "EASY, MEDIUM, HARD"
        integer order
        UUID testId FK
        UUID createdBy FK
        UUID updatedBy FK
        timestamp createdAt
        timestamp updatedAt
    }
    TEST              ||--o{ QUESTION : "contains"
    USER              ||--o{ QUESTION : "created_question"
    USER              ||--o{ QUESTION : "updated_question"


    ANSWER {
        UUID testId FK "Part of composite PK"
        UUID questionId FK "Part of composite PK"
        UUID candidateId FK "Part of composite PK, references USER(id)"
        jsonb answer_content
        boolean isCorrect
        float score
        timestamp createdAt
    }
    TEST              ||--o{ ANSWER : "collects_answers_for"
    QUESTION          ||--o{ ANSWER : "is_answer_to"
    USER              ||--o{ ANSWER : "submits (as_candidate)"


    TEST_ASSIGNMENT {
        UUID id PK
        string candidateEmail
        string status "PENDING, IN_PROGRESS, COMPLETED"
        timestamp startTime
        timestamp endTime
        float score
        UUID testId FK
        UUID createdBy FK
        UUID updatedBy FK
        timestamp createdAt
        timestamp updatedAt
    }
    TEST              ||--o{ TEST_ASSIGNMENT : "has_assignments"
    USER              ||--o{ TEST_ASSIGNMENT : "created_assignment"
    USER              ||--o{ TEST_ASSIGNMENT : "updated_assignment"
```