ALTER TABLE subscription_plans
ADD COLUMN price_monthly_cents INTEGER,
ADD COLUMN price_yearly_cents INTEGER,
ADD COLUMN stripe_product_id VARCHAR(255),
ADD COLUMN stripe_price_id VARCHAR(255);

-- Ensure price consistency
ALTER TABLE subscription_plans
ADD CONSTRAINT chk_stripe_price_consistency
CHECK (
    (price_monthly_cents IS NULL AND stripe_price_id IS NULL) OR
    (price_monthly_cents IS NOT NULL AND price_monthly_cents >= 0)
);

CREATE INDEX idx_subscription_plans_stripe_product ON subscription_plans(stripe_product_id);
CREATE INDEX idx_subscription_plans_stripe_price ON subscription_plans(stripe_price_id);

ALTER TABLE user_subscriptions
ADD COLUMN stripe_subscription_id VARCHAR(255) UNIQUE,
ADD COLUMN stripe_customer_id VARCHAR(255),
ADD COLUMN current_period_start TIMESTAMP WITH TIME ZONE,
ADD COLUMN current_period_end TIMESTAMP WITH TIME ZONE,
ADD COLUMN trial_end TIMESTAMP WITH TIME ZONE,
ADD COLUMN payment_method_id VARCHAR(255);

CREATE INDEX idx_user_subscriptions_stripe_subscription ON user_subscriptions(stripe_subscription_id);
CREATE INDEX idx_user_subscriptions_stripe_customer ON user_subscriptions(stripe_customer_id);
CREATE INDEX idx_user_subscriptions_trial_end ON user_subscriptions(trial_end);
CREATE INDEX idx_user_subscriptions_period_end ON user_subscriptions(current_period_end);

CREATE TABLE payment_events (
    event_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    stripe_event_id VARCHAR(255) NOT NULL UNIQUE,
    event_type VARCHAR(100) NOT NULL,
    subscription_id UUID REFERENCES user_subscriptions(subscription_id) ON DELETE CASCADE,
    processed BOOLEAN NOT NULL DEFAULT FALSE,
    processed_at TIMESTAMP WITH TIME ZONE,
    raw_data JSONB,
    error_message TEXT,
    retry_count INTEGER NOT NULL DEFAULT 0 CHECK (retry_count >= 0 AND retry_count <= 10),
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_payment_events_stripe_id ON payment_events(stripe_event_id);
CREATE INDEX idx_payment_events_processed ON payment_events(processed);
CREATE INDEX idx_payment_events_created ON payment_events(created_at);
CREATE INDEX idx_payment_events_type ON payment_events(event_type);
CREATE INDEX idx_payment_events_retry ON payment_events(retry_count);
CREATE INDEX idx_payment_events_subscription ON payment_events(subscription_id);
CREATE INDEX idx_user_subscriptions_status_user ON user_subscriptions(status, user_id);
CREATE INDEX idx_payment_events_processed_created ON payment_events(processed, created_at);
CREATE INDEX idx_payment_events_retry_unprocessed ON payment_events(retry_count) WHERE processed = FALSE;

-- Free plan
UPDATE subscription_plans
SET price_monthly_cents = 0,
    price_yearly_cents = 0
WHERE name = 'free';

-- Pro plan
UPDATE subscription_plans
SET price_monthly_cents = 2900,  -- $29.00
    price_yearly_cents = 29000   -- $290.00 (17% discount)
WHERE name = 'pro';

ALTER TABLE user_subscriptions
ADD CONSTRAINT chk_valid_status
CHECK (status IN ('active', 'cancelled', 'expired', 'suspended', 'trial'));

ALTER TABLE payment_events
ADD CONSTRAINT chk_supported_event_types
CHECK (
    event_type IN (
        'checkout.session.completed',
        'checkout.session.expired',
        'customer.subscription.created',
        'customer.subscription.deleted',
        'customer.subscription.updated',
        'invoice.payment_failed',
        'invoice.payment_succeeded'
    )
);

-- Stripe subscription uniqueness
ALTER TABLE user_subscriptions
ADD CONSTRAINT chk_stripe_subscription_unique
UNIQUE (stripe_subscription_id);

-- clean up old payment events (older than 90 days)
CREATE OR REPLACE FUNCTION cleanup_old_payment_events()
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM payment_events
    WHERE processed = TRUE
    AND created_at < CURRENT_TIMESTAMP - INTERVAL '90 days';

    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- mark failed webhooks as unprocessable after max retries
CREATE OR REPLACE FUNCTION mark_failed_webhooks()
RETURNS INTEGER AS $$
DECLARE
    updated_count INTEGER;
BEGIN
    UPDATE payment_events
    SET processed = TRUE,
        processed_at = CURRENT_TIMESTAMP,
        error_message = COALESCE(error_message, '') || ' [MAX_RETRIES_EXCEEDED]'
    WHERE processed = FALSE
    AND retry_count >= 10
    AND created_at < CURRENT_TIMESTAMP - INTERVAL '1 hour';

    GET DIAGNOSTICS updated_count = ROW_COUNT;
    RETURN updated_count;
END;
$$ LANGUAGE plpgsql;

-- get subscription metrics
CREATE OR REPLACE FUNCTION get_subscription_metrics()
RETURNS TABLE (
    total_subscriptions BIGINT,
    active_subscriptions BIGINT,
    trial_subscriptions BIGINT,
    cancelled_subscriptions BIGINT,
    stripe_managed_subscriptions BIGINT,
    revenue_monthly_cents BIGINT
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        COUNT(*) as total_subscriptions,
        COUNT(*) FILTER (WHERE us.status = 'active') as active_subscriptions,
        COUNT(*) FILTER (WHERE us.status = 'trial') as trial_subscriptions,
        COUNT(*) FILTER (WHERE us.status = 'cancelled') as cancelled_subscriptions,
        COUNT(*) FILTER (WHERE us.stripe_subscription_id IS NOT NULL) as stripe_managed_subscriptions,
        COALESCE(SUM(sp.price_monthly_cents) FILTER (WHERE us.status IN ('active', 'trial')), 0) as revenue_monthly_cents
    FROM user_subscriptions us
    JOIN subscription_plans sp ON us.plan_id = sp.plan_id;
END;
$$ LANGUAGE plpgsql;

-- Comments
COMMENT ON TABLE payment_events IS 'Stores Stripe webhook events for audit trail and processing. Payment events are automatically deleted when associated subscriptions are deleted.';
COMMENT ON COLUMN subscription_plans.price_monthly_cents IS 'Monthly price in cents (USD)';
COMMENT ON COLUMN subscription_plans.price_yearly_cents IS 'Yearly price in cents (USD)';
COMMENT ON COLUMN subscription_plans.stripe_product_id IS 'Stripe product ID for this plan';
COMMENT ON COLUMN subscription_plans.stripe_price_id IS 'Stripe price ID for monthly billing';
COMMENT ON COLUMN user_subscriptions.stripe_subscription_id IS 'Stripe subscription ID for paid plans';
COMMENT ON COLUMN user_subscriptions.stripe_customer_id IS 'Stripe customer ID';
COMMENT ON COLUMN user_subscriptions.trial_end IS 'End date of trial period if applicable';
COMMENT ON FUNCTION cleanup_old_payment_events() IS 'Removes processed payment events older than 90 days';
COMMENT ON FUNCTION get_subscription_metrics() IS 'Returns subscription and revenue metrics';
