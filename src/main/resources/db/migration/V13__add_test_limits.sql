-- Add max_tests column to subscription_plans table
ALTER TABLE subscription_plans 
ADD COLUMN max_tests INTEGER NOT NULL DEFAULT 0;

-- Update existing plans with test limits
-- Free plan: Max 2 tests per organization
UPDATE subscription_plans 
SET max_tests = 2 
WHERE name = 'free';

-- Pro plan: Max 15 tests per organization  
UPDATE subscription_plans 
SET max_tests = 15 
WHERE name = 'pro';

-- Enterprise plan: Unlimited tests (set to high number)
UPDATE subscription_plans 
SET max_tests = 999999 
WHERE name = 'enterprise';

-- Add constraint to ensure max_tests is non-negative
ALTER TABLE subscription_plans 
ADD CONSTRAINT chk_max_tests_non_negative 
CHECK (max_tests >= 0);
