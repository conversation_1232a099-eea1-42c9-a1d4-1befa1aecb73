-- Permissions table
CREATE TABLE permissions
(
    permission_id   BIGSERIAL PRIMARY KEY,
    permission_name VARCHAR(100) NOT NULL UNIQUE,
    description     TEXT,
    category        VARCHAR(50)  NOT NULL
);

-- Role-permissions junction table
CREATE TABLE role_permissions
(
    role_id       BIGINT NOT NULL REFERENCES roles (role_id) ON DELETE CASCADE,
    permission_id BIGINT NOT NULL REFERENCES permissions (permission_id) ON DELETE CASCADE,
    PRIMARY KEY (role_id, permission_id)
);

-- Insert initial permissions
INSERT INTO permissions (permission_name, description, category)
VALUES
('USER_MANAGE', 'Manage user accounts', 'ADMINISTRATION'),
('R<PERSON><PERSON>_MANAGE', 'Manage roles and permissions', 'ADMINISTRATION'),
('CREATE_ORG', 'Create an organization', 'USER');

-- Map permissions to roles
-- ADMIN gets all permissions
INSERT INTO role_permissions (role_id, permission_id)
SELECT r.role_id, p.permission_id
FROM roles r,
     permissions p
WHERE r.name = 'ROLE_ADMIN';

-- User gets "User" permissions
INSERT INTO role_permissions (role_id, permission_id)
SELECT r.role_id, p.permission_id
FROM roles r,
     permissions p
WHERE r.name = 'ROLE_USER'
  AND p.category = 'USER';
