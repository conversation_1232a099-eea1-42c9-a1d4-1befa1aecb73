-- Questions table
CREATE TABLE questions (
    question_id UUID PRIMARY KEY,
    test_id UUID NOT NULL REFERENCES tests(test_id) ON DELETE CASCADE,
    type VARCHAR(20) NOT NULL,
    text TEXT NOT NULL,
    options JSONB,
    correct_answer JSONB,
    difficulty VARCHAR(20) NOT NULL,
    "order" INTEGER NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL,
    created_by UUID NOT NULL REFERENCES users(user_id),
    updated_by UUID NOT NULL REFERENCES users(user_id),
    version BIGINT NOT NULL DEFAULT 0
);

CREATE INDEX idx_question_test_id ON questions(test_id);
CREATE INDEX idx_question_type ON questions(type);
CREATE INDEX idx_question_difficulty ON questions(difficulty);
CREATE INDEX idx_question_order ON questions("order");
