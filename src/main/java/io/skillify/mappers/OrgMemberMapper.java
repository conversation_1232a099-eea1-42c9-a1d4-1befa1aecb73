package io.skillify.mappers;

import java.util.Set;
import java.util.stream.Collectors;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.springframework.beans.factory.annotation.Autowired;

import io.skillify.dtos.org.OrgDto;
import io.skillify.models.org.OrgMember;
import io.skillify.models.org.OrganizationRole;
import io.skillify.services.org.OrgRoleService;

@Mapper(componentModel = "spring", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public abstract class OrgMemberMapper {

    @Autowired
    private OrgRoleService orgRoleService;

    @Mapping(target = "userId", source = "user.id")
    @Mapping(target = "email", source = "user.email")
    @Mapping(target = "roles", expression = "java(getRoles(member))")
    public abstract OrgDto.DetailedResponse.MemberDetails toMemberDetails(OrgMember member);

    @Named("mapMembersToMemberDetails")
    public Set<OrgDto.DetailedResponse.MemberDetails> toMemberDetailsSet(Set<OrgMember> members) {
        if (members == null) {
            return null;
        }

        return members.stream()
            .map(this::toMemberDetails)
            .collect(Collectors.toSet());
    }

    @Mapping(target = "userId", source = "user.id")
    @Mapping(target = "username", source = "user.username")
    @Mapping(target = "email", source = "user.email")
    @Mapping(target = "roles", expression = "java(getRoles(member))")
    public abstract OrgDto.MemberResponse toMemberResponse(OrgMember member);

    protected Set<String> getRoles(OrgMember member) {
        return orgRoleService.getUserRoles(member.getOrganization(), member.getUser())
            .stream()
            .map(OrganizationRole::getName)
            .collect(Collectors.toSet());
    }
}
