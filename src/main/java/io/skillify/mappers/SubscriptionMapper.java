package io.skillify.mappers;

import java.util.List;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.NullValuePropertyMappingStrategy;

import io.skillify.dtos.subscription.SubscriptionDto;
import io.skillify.models.subscription.SubscriptionPlan;
import io.skillify.models.subscription.SubscriptionStatus;
import io.skillify.models.subscription.UserSubscription;

@Mapper(componentModel = "spring", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface SubscriptionMapper {

    // From SubscriptionStatus to StatusResponse DTO
    @Mapping(target = "planName", source = "planName")
    @Mapping(target = "displayName", source = "displayName")
    @Mapping(target = "status", source = "status")
    @Mapping(target = "startedAt", source = "startedAt")
    @Mapping(target = "endedAt", source = "endedAt")
    @Mapping(target = "currentPeriodStart", source = "currentPeriodStart")
    @Mapping(target = "currentPeriodEnd", source = "currentPeriodEnd")
    @Mapping(target = "trialEnd", source = "trialEnd")
    @Mapping(target = "currentOrganizations", source = "currentOrganizationCount")
    @Mapping(target = "maxOrganizations", source = "maxOrganizations")
    @Mapping(target = "hasReachedLimit", expression = "java(status.hasReachedLimit())")
    @Mapping(target = "canCreateOrganization", expression = "java(status.canCreateOrganization())")
    @Mapping(target = "usagePercentage", expression = "java(status.getUsagePercentage())")
    @Mapping(target = "isTrial", expression = "java(status.isTrial())")
    @Mapping(target = "isActiveAndValid", expression = "java(status.isActiveAndValid())")
    @Mapping(target = "daysRemaining", expression = "java(status.getDaysRemaining())")
    SubscriptionDto.StatusResponse toStatusResponse(SubscriptionStatus status);

    // From UserSubscription to Response DTO
    @Mapping(target = "id", source = "id")
    @Mapping(target = "plan", source = "plan")
    @Mapping(target = "status", source = "status")
    @Mapping(target = "startedAt", source = "startedAt")
    @Mapping(target = "endedAt", source = "endedAt")
    @Mapping(target = "currentPeriodStart", source = "currentPeriodStart")
    @Mapping(target = "currentPeriodEnd", source = "currentPeriodEnd")
    @Mapping(target = "trialEnd", source = "trialEnd")
    @Mapping(target = "createdAt", source = "createdAt")
    @Mapping(target = "updatedAt", source = "updatedAt")
    SubscriptionDto.Response toResponse(UserSubscription subscription);

    // From SubscriptionPlan to PlanResponse DTO
    @Mapping(target = "id", source = "id")
    @Mapping(target = "name", source = "name")
    @Mapping(target = "displayName", source = "displayName")
    @Mapping(target = "maxOrganizations", source = "maxOrganizations")
    @Mapping(target = "isActive", source = "isActive")
    SubscriptionDto.PlanResponse toPlanResponse(SubscriptionPlan plan);

    List<SubscriptionDto.Response> toResponseList(List<UserSubscription> subscriptions);

    default SubscriptionDto.AvailablePlanResponse toAvailablePlanResponse(
            SubscriptionPlan plan,
            boolean canStartTrial,
            boolean hasUsedTrial,
            boolean isCurrentPlan,
            String restrictionReason) {

        return SubscriptionDto.AvailablePlanResponse.builder()
                .plan(toPlanResponse(plan))
                .canStartTrial(canStartTrial)
                .hasUsedTrial(hasUsedTrial)
                .isCurrentPlan(isCurrentPlan)
                .restrictionReason(restrictionReason)
                .build();
    }
}
