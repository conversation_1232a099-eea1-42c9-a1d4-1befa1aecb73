package io.skillify.mappers;

import io.skillify.dtos.role.PermissionDto;
import io.skillify.models.user.Permission;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

import java.util.Set;

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface PermissionMapper {

    PermissionDto toPermissionDto(Permission permission);

    Set<PermissionDto> toPermissionDtoSet(Set<Permission> permissions);
}
