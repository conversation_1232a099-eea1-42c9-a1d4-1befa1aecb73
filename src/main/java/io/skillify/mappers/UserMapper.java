package io.skillify.mappers;

import java.util.Set;

import org.mapstruct.BeanMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.Named;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.ReportingPolicy;

import io.skillify.dtos.user.UserDto;
import io.skillify.models.user.User;

@Mapper(componentModel = "spring", uses = {
        RoleMapper.class }, unmappedTargetPolicy = ReportingPolicy.IGNORE, nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface UserMapper {

    // From entity to UserResponseDto
    @Mapping(target = "roles", source = "roles")
    @Mapping(target = "username", source = "user", qualifiedByName = "extractUsername")
    UserDto.UserResponseDto toUserResponseDto(User user);

    // From entity to Set of UserResponseDto
    Set<UserDto.UserResponseDto> toUserResponseDtoSet(Set<User> users);

    // From UserCreateRequestDto to User entity
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "password", source = "password")
    @Mapping(target = "verified", source = "verified", defaultValue = "false")
    @Mapping(target = "roles", ignore = true)
    @Mapping(target = "lastLogin", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "updatedAt", ignore = true)
    @Mapping(target = "passwordResetToken", ignore = true)
    @Mapping(target = "passwordResetTokenExpiry", ignore = true)
    @Mapping(target = "emailVerificationToken", ignore = true)
    @Mapping(target = "emailVerificationTokenExpiry", ignore = true)
    @Mapping(target = "orgMemberships", ignore = true)
    @Mapping(target = "oauthId", ignore = true)
    User toUser(UserDto.UserCreateRequestDto createRequestDto);

    // From UserProfileUpdateRequestDto to User entity
    @BeanMapping(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "password", ignore = true)
    @Mapping(target = "roles", ignore = true)
    @Mapping(target = "verified", ignore = true)
    @Mapping(target = "lastLogin", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "updatedAt", ignore = true)
    @Mapping(target = "oauthId", ignore = true)
    @Mapping(target = "emailVerificationToken", ignore = true)
    @Mapping(target = "emailVerificationTokenExpiry", ignore = true)
    @Mapping(target = "passwordResetToken", ignore = true)
    @Mapping(target = "passwordResetTokenExpiry", ignore = true)
    @Mapping(target = "orgMemberships", ignore = true)
    void updateUserProfileFromDto(UserDto.UserProfileUpdateRequestDto updateRequestDto, @MappingTarget User user);

    // From UserDto.AdminUserUpdateRequestDto to User entity
    @BeanMapping(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "password", ignore = true)
    @Mapping(target = "roles", ignore = true)
    @Mapping(target = "verified", source = "verified")
    @Mapping(target = "lastLogin", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "updatedAt", ignore = true)
    @Mapping(target = "oauthId", ignore = true)
    @Mapping(target = "emailVerificationToken", ignore = true)
    @Mapping(target = "emailVerificationTokenExpiry", ignore = true)
    @Mapping(target = "passwordResetToken", ignore = true)
    @Mapping(target = "passwordResetTokenExpiry", ignore = true)
    @Mapping(target = "orgMemberships", ignore = true)
    void adminUpdateUserFromDto(UserDto.AdminUserUpdateRequestDto updateRequestDto, @MappingTarget User user);

    @Named("extractUsername")
    default String extractUsername(User user) {
        return user.getName();
    }
}
