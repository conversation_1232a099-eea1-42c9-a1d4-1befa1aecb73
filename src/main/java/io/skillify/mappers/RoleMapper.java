package io.skillify.mappers;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;

import io.skillify.dtos.role.RoleDto;
import io.skillify.models.user.Role;

@Mapper(componentModel = "spring", uses = {PermissionMapper.class}, unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface RoleMapper {

    // From entity to RoleResponseDto
    RoleDto.RoleResponseDto toRoleResponseDto(Role role);

    // From RoleCreateRequestDto to Role entity
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "permissions", ignore = true)
    Role toRole(RoleDto.RoleCreateRequestDto createRequestDto);

    // From RoleUpdateRequestDto to Role entity
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "permissions", ignore = true)
    void updateRoleFromDto(RoleDto.RoleUpdateRequestDto updateRequestDto, @MappingTarget Role role);

}
