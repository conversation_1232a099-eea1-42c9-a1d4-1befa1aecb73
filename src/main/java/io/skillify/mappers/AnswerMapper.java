package io.skillify.mappers;

import java.util.List;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;

import io.skillify.dtos.question.AnswerDto;
import io.skillify.dtos.question.AnswerDto.AnswerRequest;
import io.skillify.dtos.question.AnswerDto.SubmitAnswerRequest;
import io.skillify.models.question.Answer;

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface AnswerMapper {

    @Mapping(target = "testId", source = "testId")
    @Mapping(target = "questionId", source = "questionId")
    @Mapping(target = "candidateId", source = "candidateId")
    @Mapping(target = "answer", source = "answer")
    AnswerDto.Response toDto(Answer answer);

    List<AnswerDto.Response> toDtoList(List<Answer> answers);

    @Mapping(target = "testId", source = "testId")
    @Mapping(target = "questionId", source = "questionId")
    @Mapping(target = "test", ignore = true)
    @Mapping(target = "question", ignore = true)
    @Mapping(target = "candidate", ignore = true)
    @Mapping(target = "candidateId", ignore = true)
    @Mapping(target = "isCorrect", ignore = true)
    @Mapping(target = "score", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "version", ignore = true)
    Answer toEntity(SubmitAnswerRequest request);

    @Mapping(target = "testId", ignore = true)
    @Mapping(target = "questionId", source = "questionId")
    @Mapping(target = "answer", source = "answer")
    @Mapping(target = "test", ignore = true)
    @Mapping(target = "question", ignore = true)
    @Mapping(target = "candidate", ignore = true)
    @Mapping(target = "candidateId", ignore = true)
    @Mapping(target = "isCorrect", ignore = true)
    @Mapping(target = "score", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "version", ignore = true)
    Answer toEntityFromBatchRequest(AnswerRequest request);
}
