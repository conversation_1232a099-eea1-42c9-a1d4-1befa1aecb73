package io.skillify.mappers;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValuePropertyMappingStrategy;

import io.skillify.dtos.org.OrgDto;
import io.skillify.models.org.Organization;

@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        uses = {OrgMemberMapper.class})
public interface OrgMapper {

    @Mapping(target = "id", source = "id")
    @Mapping(target = "name", source = "name")
    @Mapping(target = "createdBy", source = "createdBy.email")
    @Mapping(target = "members", source = "members", qualifiedByName = "mapMembersToMemberDetails")
    OrgDto.DetailedResponse toDetailedResponse(Organization org);

    @Mapping(target = "id", source = "id")
    @Mapping(target = "name", source = "name")
    @Mapping(target = "createdBy", source = "createdBy.email")
    @Mapping(target = "memberCount", expression = "java(org.getMembers().size())")
    OrgDto.Response toResponse(Organization org);

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "members", ignore = true)
    @Mapping(target = "jobs", ignore = true)
    Organization toEntity(OrgDto.CreateRequest createRequest);

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "members", ignore = true)
    @Mapping(target = "jobs", ignore = true)
    void updateEntityFromDto(OrgDto.UpdateRequest updateRequest, @MappingTarget Organization organization);
}
