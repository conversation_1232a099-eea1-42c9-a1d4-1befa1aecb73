package io.skillify.mappers;

import java.time.format.DateTimeFormatter;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.NullValuePropertyMappingStrategy;

import com.stripe.model.checkout.Session;

import io.skillify.dtos.payment.PaymentDto;
import io.skillify.models.subscription.PaymentEvent;
import io.skillify.services.payment.PaymentService;

@Mapper(componentModel = "spring", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface PaymentMapper {

    // Stripe Session to Checkout Response
    @Mapping(target = "sessionId", source = "id")
    @Mapping(target = "url", source = "url")
    @Mapping(target = "expiresAt", source = "expiresAt")
    PaymentDto.CheckoutSessionResponse toCheckoutResponse(Session session);

    // CheckoutSuccessData to Checkout Success Response
    @Mapping(target = "success", constant = "true")
    @Mapping(target = "sessionId", source = "session.id")
    @Mapping(target = "url", source = "session.url")
    @Mapping(target = "expiresAt", source = "session.expiresAt")
    @Mapping(target = "subscriptionId", source = "subscription.id")
    @Mapping(target = "planName", source = "subscription.plan.displayName")
    @Mapping(target = "message", expression = "java(buildSuccessMessage(data.subscription().getPlan().getDisplayName()))")
    @Mapping(target = "startedAt", source = "subscription.startedAt", qualifiedByName = "formatDateTime")
    @Mapping(target = "currentPeriodEnd", source = "subscription.currentPeriodEnd", qualifiedByName = "formatDateTime")
    @Mapping(target = "trialEnd", source = "subscription.trialEnd", qualifiedByName = "formatDateTime")
    PaymentDto.CheckoutSuccessResponse toCheckoutSuccessResponse(PaymentService.CheckoutSuccessData data);

    // PaymentEvent to simple response (for admin/debugging)
    @Mapping(target = "id", source = "id")
    @Mapping(target = "stripeEventId", source = "stripeEventId")
    @Mapping(target = "eventType", source = "eventType")
    @Mapping(target = "processed", source = "processed")
    @Mapping(target = "createdAt", source = "createdAt", qualifiedByName = "formatDateTime")
    @Mapping(target = "processedAt", source = "processedAt", qualifiedByName = "formatDateTime")
    @Mapping(target = "errorMessage", source = "errorMessage")
    PaymentDto.PaymentEventResponse toPaymentEventResponse(PaymentEvent event);

    @Named("formatDateTime")
    default String formatDateTime(java.time.OffsetDateTime dateTime) {
        return dateTime != null ? dateTime.format(DateTimeFormatter.ISO_OFFSET_DATE_TIME) : null;
    }

    default String buildSuccessMessage(String planName) {
        if (planName == null || planName.isBlank()) {
            return "Welcome to Skillify! Your subscription is now active.";
        }
        return String.format("%s", planName);
    }

    default PaymentDto.CheckoutSuccessResponse toErrorResponse(String error) {
        return PaymentDto.CheckoutSuccessResponse.builder()
                .success(false)
                .message(error)
                .build();
    }

    // CancelSubscriptionData to Cancel Subscription Response
    @Mapping(target = "message", expression = "java(buildCancelMessage(data.cancelledSubscription().getPlan().getDisplayName(), immediate))")
    @Mapping(target = "subscriptionId", source = "data.cancelledSubscription.id")
    @Mapping(target = "planName", source = "data.cancelledSubscription.plan.displayName")
    @Mapping(target = "status", source = "data.cancelledSubscription.status")
    @Mapping(target = "immediate", source = "immediate")
    @Mapping(target = "endsAt", expression = "java(determineEndsAt(data.cancelledSubscription(), immediate))")
    @Mapping(target = "freePlanSubscriptionId", source = "data.freePlanSubscription.id")
    PaymentDto.CancelSubscriptionResponse toCancelSubscriptionResponse(PaymentService.CancelSubscriptionData data,
            Boolean immediate);

    default String determineEndsAt(io.skillify.models.subscription.UserSubscription subscription, Boolean immediate) {
        if (immediate != null && immediate) {
            return formatDateTime(subscription.getEndedAt());
        } else {
            return formatDateTime(subscription.getCurrentPeriodEnd());
        }
    }

    default String buildCancelMessage(String planName, Boolean immediate) {
        if (immediate != null && immediate) {
            return String.format(
                    "Your %s subscription has been cancelled immediately. You've been downgraded to the free plan.",
                    planName);
        }
        return String.format(
                "Your %s subscription will be cancelled at the end of your billing period. You'll continue to have %s access until then.",
                planName, planName);
    }
}
