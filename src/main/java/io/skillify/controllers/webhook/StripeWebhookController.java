package io.skillify.controllers.webhook;

import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import io.skillify.services.payment.WebhookProcessingService;
import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RestController
@RequestMapping("/api/webhooks")
@Tag(name = "Webhooks", description = "Payment webhook endpoints")
// @Hidden
public class StripeWebhookController {

    private final WebhookProcessingService webhookProcessingService;
    private final String webhookSecret;

    public StripeWebhookController(
            WebhookProcessingService webhookProcessingService,
            @Qualifier("stripeWebhookSecret") String webhookSecret) {
        this.webhookProcessingService = webhookProcessingService;
        this.webhookSecret = webhookSecret;
    }

    @PostMapping("/stripe")
    @Operation(summary = "Handle Stripe webhook events", description = "Processes webhook events from Stripe for subscription management")
    @ApiResponses({
            @ApiResponse(responseCode = "200", description = "Webhook processed successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid webhook signature"),
            @ApiResponse(responseCode = "500", description = "Internal processing error")
    })
    public ResponseEntity<String> handleStripeWebhook(
            @RequestBody String payload,
            @RequestHeader("Stripe-Signature") String signature) {

        if (payload == null || payload.isBlank()) {
            log.error("Received empty webhook payload");
            return ResponseEntity.badRequest().body("Empty payload");
        }

        if (signature == null || signature.isBlank()) {
            log.error("Missing Stripe signature header");
            return ResponseEntity.badRequest().body("Missing signature");
        }

        try {
            boolean processed = webhookProcessingService.processWebhook(payload, signature, webhookSecret);

            if (processed) {
                return ResponseEntity.ok("Event received");
            } else {
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                        .body("Processing failed");
            }

        } catch (IllegalArgumentException e) {
            log.error("Invalid webhook data: {}", e.getMessage());
            return ResponseEntity.badRequest().body("Invalid data");
        } catch (Exception e) {
            log.error("Webhook processing error for payload length {}: {}",
                    payload.length(), e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("Processing error");
        }
    }

    @PostMapping("/stripe/test")
    @Operation(summary = "Test webhook endpoint", description = "Test endpoint for webhook configuration validation")
    @ApiResponses({
            @ApiResponse(responseCode = "200", description = "Test successful"),
            @ApiResponse(responseCode = "400", description = "Test failed")
    })
    public ResponseEntity<String> testWebhook() {
        log.info("Webhook test endpoint called");
        return ResponseEntity.ok("Webhook endpoint is working");
    }
}
