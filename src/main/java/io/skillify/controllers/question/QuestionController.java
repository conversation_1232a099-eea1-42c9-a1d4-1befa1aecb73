package io.skillify.controllers.question;

import java.util.UUID;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import io.skillify.dtos.ResponseDto;
import io.skillify.dtos.question.QuestionDto;
import io.skillify.dtos.question.QuestionDto.BaseQuestion;
import io.skillify.dtos.question.QuestionDto.BatchCreateQuestionsRequest;
import io.skillify.dtos.question.QuestionDto.BatchCreateQuestionsResponse;
import io.skillify.dtos.question.QuestionDto.BatchUpdateQuestionsRequest;
import io.skillify.dtos.question.QuestionDto.BatchUpdateQuestionsResponse;
import io.skillify.dtos.question.QuestionDto.CreateQuestionRequest;
import io.skillify.services.question.AnswerService;
import io.skillify.services.question.QuestionService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;

@RestController
@RequiredArgsConstructor
@Tag(name = "Questions", description = "API for managing questions within a test")
public class QuestionController {

    private final QuestionService questionService;
    Logger logger = LoggerFactory.getLogger(QuestionController.class);

    @PostMapping("/api/tests/{testId}/questions")
    @PreAuthorize("@orgPermissionEvaluator.hasPermissionForTest(#testId, 'MANAGE_TEST')")
    @Operation(summary = "Create a new question for a test", description = "Creates a new question for the specified test")
    public ResponseEntity<ResponseDto<QuestionDto.Response>> createQuestion(
            @Parameter(description = "ID of the test to add the question to", required = true) @PathVariable UUID testId,
            @Valid @RequestBody CreateQuestionRequest request) {
        QuestionDto.Response createdQuestion = questionService.createQuestion(testId, request);
        return ResponseEntity.status(HttpStatus.CREATED)
                .body(ResponseDto.success(createdQuestion));
    }

    @PostMapping("/api/tests/{testId}/questions/batch")
    @PreAuthorize("@orgPermissionEvaluator.hasPermissionForTest(#testId, 'MANAGE_TEST')")
    @Operation(
        summary = "Create multiple questions for a test in one operation",
        description = "Creates multiple questions for the specified test in a single request"
    )
    public ResponseEntity<ResponseDto<BatchCreateQuestionsResponse>> batchCreateQuestions(
            @Parameter(description = "ID of the test to add questions to", required = true) @PathVariable UUID testId,
            @Valid @RequestBody BatchCreateQuestionsRequest request) {
        logger.info("Received batch create request for {} questions for test {}",
                request.getQuestions().size(), testId);

        BatchCreateQuestionsResponse response = questionService.batchCreateQuestions(testId, request);
        return ResponseEntity.status(HttpStatus.CREATED)
                .body(ResponseDto.success(response));
    }

    @DeleteMapping("/api/tests/{testId}/questions/batch")
    @PreAuthorize("@orgPermissionEvaluator.hasPermissionForTest(#testId, 'MANAGE_TEST')")
    @Operation(summary = "Delete multiple questions from a test")
    public ResponseEntity<ResponseDto<?>> batchDeleteQuestions(
            @Parameter(description = "ID of the test to delete questions from", required = true) @PathVariable UUID testId,
            @Valid @RequestBody QuestionDto.BatchDeleteQuestionsRequest request) {
        logger.info("Received batch delete request for {} questions from test {}",
                request.getQuestionIds().size(), testId);

        QuestionDto.BatchDeleteQuestionsResponse response = questionService.batchDeleteQuestions(testId, request);
        return ResponseEntity.ok(ResponseDto.success(response));
    }

    @PatchMapping("/api/tests/{testId}/questions/batch")
    @PreAuthorize("@orgPermissionEvaluator.hasPermissionForTest(#testId, 'MANAGE_TEST')")
    @Operation(
        summary = "Update multiple questions for a test in one operation",
        description = "Updates, creates, or deletes multiple questions for the specified test based on the provided list."
    )
    public ResponseEntity<ResponseDto<BatchUpdateQuestionsResponse>> batchUpdateQuestions(
            @Parameter(description = "ID of the test to update questions for", required = true) @PathVariable UUID testId,
            @Valid @RequestBody BatchUpdateQuestionsRequest request) {
        logger.info("Received batch update request for {} questions for test {}",
                request.getQuestions().size(), testId);

        BatchUpdateQuestionsResponse response = questionService.batchUpdateQuestions(testId, request);
        return ResponseEntity.ok(ResponseDto.success(response));
    }

    @GetMapping("/api/tests/{testId}/questions/{questionId}")
    @PreAuthorize("@orgPermissionEvaluator.hasPermissionForTest(#testId, 'MANAGE_TEST')")
    @Operation(summary = "Get a question by ID from a test", description = "Retrieves a specific question by its ID from the specified test")
    public ResponseEntity<ResponseDto<QuestionDto.Response>> getQuestion(
            @Parameter(description = "ID of the test", required = true) @PathVariable UUID testId,
            @Parameter(description = "ID of the question", required = true) @PathVariable UUID questionId) {
        QuestionDto.Response question = questionService.getQuestion(questionId); 
        return ResponseEntity.ok(ResponseDto.success(question));
    }

    @PatchMapping("/api/tests/{testId}/questions/{questionId}")
    @PreAuthorize("@orgPermissionEvaluator.hasPermissionForTest(#testId, 'MANAGE_TEST')")
    @Operation(summary = "Update a question in a test", description = "Updates an existing question within the specified test")
    public ResponseEntity<ResponseDto<QuestionDto.Response>> updateQuestion(
            @Parameter(description = "ID of the test", required = true) @PathVariable UUID testId,
            @Parameter(description = "ID of the question to update", required = true) @PathVariable UUID questionId,
            @Valid @RequestBody BaseQuestion request) {
        QuestionDto.Response updatedQuestion = questionService.updateQuestion(questionId, request);
        return ResponseEntity.ok(ResponseDto.success(updatedQuestion));
    }

    @DeleteMapping("/api/tests/{testId}/questions/{questionId}")
    @PreAuthorize("@orgPermissionEvaluator.hasPermissionForTest(#testId, 'MANAGE_TEST')")
    @Operation(summary = "Delete a question from a test", description = "Deletes a question by its ID from the specified test")
    public ResponseEntity<ResponseDto<Void>> deleteQuestion(
            @Parameter(description = "ID of the test", required = true) @PathVariable UUID testId,
            @Parameter(description = "ID of the question to delete", required = true) @PathVariable UUID questionId) {
        questionService.deleteQuestion(questionId);
        return ResponseEntity.ok(ResponseDto.success(null));
    }

    @GetMapping("/api/tests/{testId}/questions")
    @PreAuthorize("@orgPermissionEvaluator.hasPermissionForTest(#testId, 'MANAGE_TEST')")
    @Operation(summary = "Get test questions with filtering and sorting options", description = "Retrieves questions for a test with support for filtering and sorting via query parameters")
    @io.swagger.v3.oas.annotations.responses.ApiResponses({
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "Questions or metrics retrieved successfully"),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "400", description = "Invalid parameters"),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "403", description = "Insufficient permissions"),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "404", description = "Test not found")
    })
    public ResponseEntity<ResponseDto<?>> getQuestionsByTest(
            @Parameter(description = "ID of the test", required = true) @PathVariable UUID testId,
            @Parameter(description = "Optional metric (count)", schema = @io.swagger.v3.oas.annotations.media.Schema(allowableValues = {"count"})) 
            @RequestParam(required = false) String metric,
            @Parameter(description = "Optional difficulty filter", schema = @io.swagger.v3.oas.annotations.media.Schema(allowableValues = { "EASY", "MEDIUM", "HARD" })) 
            @RequestParam(required = false) String difficulty,
            @Parameter(description = "Optional sort by order", schema = @io.swagger.v3.oas.annotations.media.Schema(allowableValues = {"ordered" })) 
            @RequestParam(required = false) String sort,
            @Parameter(description = "Optional filter by question type") 
            @RequestParam(required = false) String type) {

        logger.info("Fetching questions for test {} with parameters - metric: {}, difficulty: {}, sort: {}, type: {}",
                testId, metric, difficulty, sort, type);

        Object result = questionService.getTestQuestionsWithFilters(testId, metric, difficulty, sort, type);
        return ResponseEntity.ok(ResponseDto.success(result));
    }
}
