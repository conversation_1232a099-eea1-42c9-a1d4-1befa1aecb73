package io.skillify.controllers.subscription;

import java.util.List;
import java.util.UUID;

import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import io.skillify.dtos.ResponseDto;
import io.skillify.dtos.subscription.SubscriptionDto;
import io.skillify.mappers.SubscriptionMapper;
import io.skillify.models.user.User;
import io.skillify.services.subscription.SubscriptionService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RestController
@RequestMapping("/api/subscriptions")
@RequiredArgsConstructor
@Tag(name = "Subscriptions", description = "Subscription management API")
@SecurityRequirement(name = "bearerAuth")
@PreAuthorize("isAuthenticated()")
public class SubscriptionController {

    private final SubscriptionService subscriptionService;
    private final SubscriptionMapper subscriptionMapper;

    @GetMapping("/status")
    @Operation(summary = "Get subscription status")
    @ApiResponses({
            @ApiResponse(responseCode = "200", description = "Subscription status retrieved successfully"),
            @ApiResponse(responseCode = "401", description = "Unauthorized")
    })
    public ResponseEntity<ResponseDto<SubscriptionDto.StatusResponse>> getSubscriptionStatus(
            @Parameter(hidden = true) @AuthenticationPrincipal User user) {

        var status = subscriptionService.getUserSubscriptionStatus(user.getId());
        var response = subscriptionMapper.toStatusResponse(status);
        return ResponseEntity.ok(ResponseDto.success(response));
    }

    @GetMapping("/history")
    @Operation(summary = "Get subscription history")
    @ApiResponses({
            @ApiResponse(responseCode = "200", description = "Subscription history retrieved successfully"),
            @ApiResponse(responseCode = "401", description = "Unauthorized")
    })
    public ResponseEntity<ResponseDto<List<SubscriptionDto.Response>>> getSubscriptionHistory(
            @Parameter(hidden = true) @AuthenticationPrincipal User user) {

        var history = subscriptionService.getUserSubscriptionHistory(user.getId());
        var response = subscriptionMapper.toResponseList(history);
        return ResponseEntity.ok(ResponseDto.success(response));
    }

    @GetMapping("/plans")
    @Operation(summary = "Get available subscription plans")
    @ApiResponses({
            @ApiResponse(responseCode = "200", description = "Available plans retrieved successfully"),
            @ApiResponse(responseCode = "401", description = "Unauthorized")
    })
    public ResponseEntity<ResponseDto<List<SubscriptionDto.AvailablePlanResponse>>> getAvailablePlans(
            @Parameter(hidden = true) @AuthenticationPrincipal User user) {

        var response = subscriptionService.getDetailedAvailablePlansForUser(user.getId());
        return ResponseEntity.ok(ResponseDto.success(response));
    }

    @PatchMapping("/update")
    @Operation(summary = "Update user subscription plan", description = "Admin endpoint to manually update any user's subscription plan for support cases")
    @ApiResponses({
            @ApiResponse(responseCode = "200", description = "Subscription updated successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid plan name or user ID"),
            @ApiResponse(responseCode = "404", description = "User or plan not found"),
            @ApiResponse(responseCode = "403", description = "Admin access required")
    })
    @PreAuthorize("hasRole('ROLE_ADMIN')")
    public ResponseEntity<ResponseDto<SubscriptionDto.Response>> updateUserSubscription(
            @Parameter(description = "ID of the user whose subscription to update") @RequestParam UUID userId,
            @Valid @RequestBody SubscriptionDto.UpdateRequest request) {

        log.info("Admin updating subscription for user {} to plan {}", userId, request.getPlanName());
        var subscription = subscriptionService.updateUserSubscription(userId, request.getPlanName());
        var response = subscriptionMapper.toResponse(subscription);
        return ResponseEntity.ok(ResponseDto.success(response));
    }
}
