package io.skillify.controllers.payment;

import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import io.skillify.dtos.ResponseDto;
import io.skillify.dtos.payment.PaymentDto;
import io.skillify.mappers.PaymentMapper;
import io.skillify.models.user.User;
import io.skillify.services.payment.PaymentService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;

@RestController
@RequestMapping("/api/payments")
@RequiredArgsConstructor
@Tag(name = "Payments", description = "Stripe payment processing")
public class PaymentController {

    private final PaymentService paymentService;
    private final PaymentMapper paymentMapper;

    @PostMapping("/checkout")
    @Operation(summary = "Create checkout session", description = "Creates a Stripe checkout session for subscription")
    @ApiResponses({
            @ApiResponse(responseCode = "200", description = "Checkout session created successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid request or plan not found"),
            @ApiResponse(responseCode = "401", description = "Authentication required")
    })
    @PreAuthorize("isAuthenticated()")
    public ResponseEntity<ResponseDto<?>> createCheckoutSession(
            @Valid @RequestBody PaymentDto.CreateCheckoutSessionRequest request,
            @AuthenticationPrincipal User user) {

        var session = paymentService.createCheckoutSession(user, request.getPlanName());
        var response = paymentMapper.toCheckoutResponse(session);
        return ResponseEntity.ok(ResponseDto.success(response));
    }

    @PostMapping("/checkout/success")
    @Operation(summary = "Verify checkout success", description = "Verifies and processes successful checkout")
    @ApiResponses({
            @ApiResponse(responseCode = "200", description = "Checkout verified successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid session or checkout failed"),
            @ApiResponse(responseCode = "401", description = "Authentication required")
    })
    @PreAuthorize("isAuthenticated()")
    public ResponseEntity<ResponseDto<PaymentDto.CheckoutSuccessResponse>> verifyCheckoutSuccess(
            @Valid @RequestBody PaymentDto.CheckoutSuccessRequest request,
            @AuthenticationPrincipal User user) {

        PaymentService.CheckoutSuccessData checkoutData = paymentService.processCheckoutSuccess(user,
                request.getSessionId());
        PaymentDto.CheckoutSuccessResponse response = paymentMapper.toCheckoutSuccessResponse(checkoutData);
        return ResponseEntity.ok(ResponseDto.success(response));
    }

    @PostMapping("/cancel")
    @Operation(summary = "Cancel subscription", description = "Cancels the user's current subscription")
    @ApiResponses({
            @ApiResponse(responseCode = "200", description = "Subscription cancelled successfully"),
            @ApiResponse(responseCode = "400", description = "No active subscription or cancellation failed"),
            @ApiResponse(responseCode = "401", description = "Authentication required")
    })
    @PreAuthorize("isAuthenticated()")
    public ResponseEntity<ResponseDto<PaymentDto.CancelSubscriptionResponse>> cancelSubscription(
            @RequestBody(required = false) PaymentDto.CancelSubscriptionRequest request,
            @AuthenticationPrincipal User user) {

        boolean immediate = request != null ? request.getImmediate() : false;
        String reason = request != null ? request.getReason() : null;

        PaymentService.CancelSubscriptionData cancelData = paymentService.cancelSubscription(user, immediate, reason);
        PaymentDto.CancelSubscriptionResponse response = paymentMapper.toCancelSubscriptionResponse(cancelData,
                immediate);

        return ResponseEntity.ok(ResponseDto.success(response));
    }

    @PostMapping("/sync-plan/{planName}")
    @Operation(summary = "Sync plan with Stripe", description = "Creates/updates Stripe product and price for a plan")
    @ApiResponses({
            @ApiResponse(responseCode = "200", description = "Plan synced successfully"),
            @ApiResponse(responseCode = "400", description = "Plan not found or sync failed")
    })
    @PreAuthorize("hasRole('ROLE_ADMIN')")
    public ResponseEntity<String> syncPlanWithStripe(@PathVariable String planName) {
        paymentService.syncPlanWithStripe(planName);
        return ResponseEntity.ok("Plan " + planName + " synced with Stripe successfully");
    }
}
