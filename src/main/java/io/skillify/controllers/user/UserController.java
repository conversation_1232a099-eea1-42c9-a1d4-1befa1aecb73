package io.skillify.controllers.user;

import java.util.UUID;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import io.skillify.dtos.ResponseDto;
import io.skillify.dtos.user.UserDto;
import io.skillify.models.user.User;
import io.skillify.services.user.UserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;

@RestController
@RequiredArgsConstructor
@Tag(name = "Users", description = "User management and self-service API")
public class UserController {

    private final UserService userService;

    @GetMapping("/api/users/me")
    @PreAuthorize("isAuthenticated()")
    @Operation(summary = "Get current user details", security = @SecurityRequirement(name = "bearerAuth"))
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Current user details retrieved"),
            @ApiResponse(responseCode = "401", description = "Unauthorized if not logged in", content = @Content)
    })
    public ResponseEntity<ResponseDto<UserDto.UserResponseDto>> getCurrentUserDetails() {
        UserDto.UserResponseDto userDetails = userService.getCurrentUserDetails();
        return ResponseEntity.ok(ResponseDto.success(userDetails));
    }

    @PutMapping("/api/users/me")
    @PreAuthorize("isAuthenticated()")
    @Operation(summary = "Update current user profile", security = @SecurityRequirement(name = "bearerAuth"))
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "User profile updated"),
            @ApiResponse(responseCode = "400", description = "Invalid input data", content = @Content),
            @ApiResponse(responseCode = "401", description = "Unauthorized", content = @Content),
            @ApiResponse(responseCode = "404", description = "User not found", content = @Content)
    })
    public ResponseEntity<ResponseDto<UserDto.UserResponseDto>> updateUserProfile(
            @Parameter(hidden = true) @AuthenticationPrincipal User currentUser,
            @Valid @RequestBody UserDto.UserProfileUpdateRequestDto requestDto) {
        UserDto.UserResponseDto updatedUser = userService.updateUserProfile(currentUser.getId(), requestDto);
        return ResponseEntity.ok(ResponseDto.success(updatedUser));
    }

    @PostMapping("/api/users/me/change-password")
    @PreAuthorize("isAuthenticated()")
    @Operation(summary = "Change current user password", security = @SecurityRequirement(name = "bearerAuth"))
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Password changed successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid input or old password mismatch", content = @Content),
            @ApiResponse(responseCode = "401", description = "Unauthorized", content = @Content),
            @ApiResponse(responseCode = "404", description = "User not found", content = @Content)
    })
    public ResponseEntity<ResponseDto<Object>> changeUserPassword(
            @Parameter(hidden = true) @AuthenticationPrincipal User currentUser,
            @Valid @RequestBody UserDto.PasswordChangeRequestDto requestDto) {
        userService.changeUserPassword(currentUser.getId(), requestDto);
        return ResponseEntity.ok(ResponseDto.success(null));
    }

    @PostMapping("/api/admin/users")
    @PreAuthorize("hasAuthority('USER_MANAGE')")
    @Operation(summary = "Admin: Create a new user", security = @SecurityRequirement(name = "bearerAuth"))
    @ApiResponses(value = {
            @ApiResponse(responseCode = "201", description = "User created successfully by admin"),
            @ApiResponse(responseCode = "400", description = "Invalid input data or user already exists", content = @Content),
            @ApiResponse(responseCode = "401", description = "Unauthorized", content = @Content),
            @ApiResponse(responseCode = "403", description = "Forbidden (insufficient permissions)", content = @Content)
    })
    public ResponseEntity<ResponseDto<UserDto.UserResponseDto>> adminCreateUser(
            @RequestBody @Valid UserDto.UserCreateRequestDto createRequestDto) {
        UserDto.UserResponseDto createdUser = userService.adminCreateUser(createRequestDto);
        return new ResponseEntity<>(ResponseDto.success(createdUser), HttpStatus.CREATED);
    }

    @GetMapping("/api/admin/users")
    @PreAuthorize("hasAuthority('USER_MANAGE')")
    @Operation(summary = "Admin: Get all users (paginated)", security = @SecurityRequirement(name = "bearerAuth"))
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "List of users retrieved"),
            @ApiResponse(responseCode = "401", description = "Unauthorized", content = @Content),
            @ApiResponse(responseCode = "403", description = "Forbidden", content = @Content)
    })
    @Parameter(name = "pageable", in = ParameterIn.QUERY, description = "Pagination and sorting parameters", content = @Content(schema = @Schema(implementation = Pageable.class)))
    public ResponseEntity<ResponseDto<Page<UserDto.UserResponseDto>>> getAllUsers(
            @Parameter(hidden = true) Pageable pageable) {
        Page<UserDto.UserResponseDto> usersPage = userService.getAllUsers(pageable);
        return ResponseEntity.ok(ResponseDto.success(usersPage));
    }

    @GetMapping("/api/admin/users/{userId}")
    @PreAuthorize("hasAuthority('USER_MANAGE')")
    @Operation(summary = "Admin: Get user by ID", security = @SecurityRequirement(name = "bearerAuth"))
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "User details retrieved"),
            @ApiResponse(responseCode = "401", description = "Unauthorized", content = @Content),
            @ApiResponse(responseCode = "403", description = "Forbidden", content = @Content),
            @ApiResponse(responseCode = "404", description = "User not found", content = @Content)
    })
    public ResponseEntity<ResponseDto<UserDto.UserResponseDto>> getUserById(
            @Parameter(description = "ID of the user to retrieve") @PathVariable UUID userId) {
        UserDto.UserResponseDto user = userService.getUserById(userId);
        return ResponseEntity.ok(ResponseDto.success(user));
    }

    @GetMapping("/api/admin/users/username/{username}")
    @PreAuthorize("hasAuthority('USER_MANAGE')")
    @Operation(summary = "Admin: Get user by username", security = @SecurityRequirement(name = "bearerAuth"))
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "User details retrieved"),
            @ApiResponse(responseCode = "401", description = "Unauthorized", content = @Content),
            @ApiResponse(responseCode = "403", description = "Forbidden", content = @Content),
            @ApiResponse(responseCode = "404", description = "User not found", content = @Content)
    })
    public ResponseEntity<ResponseDto<UserDto.UserResponseDto>> getUserByUsername(
            @Parameter(description = "Username of the user to retrieve") @PathVariable String username) {
        UserDto.UserResponseDto user = userService.getUserByUsername(username);
        return ResponseEntity.ok(ResponseDto.success(user));
    }

    @GetMapping("/api/admin/users/email/{email}")
    @PreAuthorize("hasAuthority('USER_MANAGE')")
    @Operation(summary = "Admin: Get user by email", security = @SecurityRequirement(name = "bearerAuth"))
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "User details retrieved"),
            @ApiResponse(responseCode = "401", description = "Unauthorized", content = @Content),
            @ApiResponse(responseCode = "403", description = "Forbidden", content = @Content),
            @ApiResponse(responseCode = "404", description = "User not found", content = @Content)
    })
    public ResponseEntity<ResponseDto<UserDto.UserResponseDto>> getUserByEmail(
            @Parameter(description = "Email of the user to retrieve") @PathVariable String email) {
        UserDto.UserResponseDto user = userService.getUserByEmail(email);
        return ResponseEntity.ok(ResponseDto.success(user));
    }

    @PutMapping("/api/admin/users/{userId}")
    @PreAuthorize("hasAuthority('USER_MANAGE')")
    @Operation(summary = "Admin: Update user details", security = @SecurityRequirement(name = "bearerAuth"))
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "User updated successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid input data or conflict", content = @Content),
            @ApiResponse(responseCode = "401", description = "Unauthorized", content = @Content),
            @ApiResponse(responseCode = "403", description = "Forbidden", content = @Content),
            @ApiResponse(responseCode = "404", description = "User not found", content = @Content)
    })
    public ResponseEntity<ResponseDto<UserDto.UserResponseDto>> adminUpdateUser(
            @Parameter(description = "ID of the user to update") @PathVariable UUID userId,
            @Valid @RequestBody UserDto.AdminUserUpdateRequestDto updateRequestDto) {
        UserDto.UserResponseDto updatedUser = userService.adminUpdateUser(userId, updateRequestDto);
        return ResponseEntity.ok(ResponseDto.success(updatedUser));
    }

    @DeleteMapping("/api/admin/users/{userId}")
    @PreAuthorize("hasAuthority('USER_MANAGE')")
    @Operation(summary = "Admin: Delete a user", security = @SecurityRequirement(name = "bearerAuth"))
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "User deleted successfully"),
            @ApiResponse(responseCode = "401", description = "Unauthorized", content = @Content),
            @ApiResponse(responseCode = "403", description = "Forbidden", content = @Content),
            @ApiResponse(responseCode = "404", description = "User not found", content = @Content)
    })
    public ResponseEntity<ResponseDto<Object>> adminDeleteUser(
            @Parameter(description = "ID of the user to delete") @PathVariable UUID userId) {
        userService.adminDeleteUser(userId);
        return ResponseEntity.ok(ResponseDto.success(null));
    }

    @PostMapping("/api/admin/users/{userId}/reset-password")
    @PreAuthorize("hasAuthority('USER_MANAGE')")
    @Operation(summary = "Admin: Reset a user's password", security = @SecurityRequirement(name = "bearerAuth"))
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "User password reset successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid new password", content = @Content),
            @ApiResponse(responseCode = "401", description = "Unauthorized", content = @Content),
            @ApiResponse(responseCode = "403", description = "Forbidden", content = @Content),
            @ApiResponse(responseCode = "404", description = "User not found", content = @Content)
    })
    public ResponseEntity<ResponseDto<Object>> adminResetUserPassword(
            @Parameter(description = "ID of the user whose password will be reset") @PathVariable UUID userId,
            @Valid @RequestBody UserDto.AdminPasswordResetRequestDto requestDto) {
        userService.adminResetUserPassword(userId, requestDto);
        return ResponseEntity.ok(ResponseDto.success(null));
    }
}
