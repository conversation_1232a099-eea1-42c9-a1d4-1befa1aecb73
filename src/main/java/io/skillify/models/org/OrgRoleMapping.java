package io.skillify.models.org;

import java.io.Serializable;
import java.util.UUID;

import io.skillify.models.user.User;
import jakarta.persistence.Embeddable;
import jakarta.persistence.EmbeddedId;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.MapsId;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Entity
@Table(name = "org_role_mapping")
@NoArgsConstructor
public class OrgRoleMapping {

    @EmbeddedId
    private OrgRoleMappingId id = new OrgRoleMappingId();

    @ManyToOne(fetch = FetchType.LAZY)
    @MapsId("organizationId")
    @JoinColumn(name = "org_id", nullable = false)
    private Organization organization;

    @ManyToOne(fetch = FetchType.LAZY)
    @MapsId("userId")
    @JoinColumn(name = "user_id", nullable = false)
    private User user;

    @ManyToOne
    @JoinColumn(name = "role_id", nullable = false)
    private OrganizationRole role;

    public OrgRoleMapping(Organization organization, User user, OrganizationRole role) {
        this.organization = organization;
        this.user = user;
        this.role = role;
        this.id = new OrgRoleMappingId(organization.getId(), user.getId());
    }

    @Embeddable
    @Data
    public static class OrgRoleMappingId implements Serializable {
        private UUID organizationId;
        private UUID userId;

        public OrgRoleMappingId() {
        }

        public OrgRoleMappingId(UUID organizationId, UUID userId) {
            this.organizationId = organizationId;
            this.userId = userId;
        }
    }
}
