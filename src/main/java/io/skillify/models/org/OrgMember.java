package io.skillify.models.org;

import java.io.Serializable;
import java.util.UUID;

import io.skillify.models.user.User;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.IdClass;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Entity
@Table(name = "org_members")
@IdClass(OrgMember.OrgMemberId.class)
@NoArgsConstructor
@AllArgsConstructor
public class OrgMember implements Serializable {
    @Id
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "org_id", nullable = false)
    private Organization organization;

    @Id
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id", nullable = false)
    private User user;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class OrgMemberId implements Serializable {
        private UUID organization;
        private UUID user;
    }
}
