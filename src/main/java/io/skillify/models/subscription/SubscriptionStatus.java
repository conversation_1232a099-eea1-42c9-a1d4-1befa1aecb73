package io.skillify.models.subscription;

import java.time.OffsetDateTime;

import io.skillify.constants.SubscriptionConstants;
import lombok.Value;

@Value
public class SubscriptionStatus {
    UserSubscription subscription;
    long currentOrganizationCount;

    public String getPlanName() {
        return subscription.getPlan().getName();
    }

    public String getDisplayName() {
        return subscription.getPlan().getDisplayName();
    }

    public int getMaxOrganizations() {
        return subscription.getPlan().getMaxOrganizations();
    }

    public String getStatus() {
        return subscription.getStatus();
    }

    public OffsetDateTime getStartedAt() {
        return subscription.getStartedAt();
    }

    public OffsetDateTime getEndedAt() {
        return subscription.getEndedAt();
    }

    public OffsetDateTime getCurrentPeriodStart() {
        return subscription.getCurrentPeriodStart();
    }

    public OffsetDateTime getCurrentPeriodEnd() {
        return subscription.getCurrentPeriodEnd();
    }

    public OffsetDateTime getTrialEnd() {
        return subscription.getTrialEnd();
    }

    public boolean hasReachedLimit() {
        return currentOrganizationCount >= getMaxOrganizations();
    }

    public boolean canCreateOrganization() {
        return !hasReachedLimit() && isActiveAndValid();
    }

    public int getUsagePercentage() {
        if (getMaxOrganizations() == 0) return 0;
        return (int) Math.min(100, (currentOrganizationCount * 100) / getMaxOrganizations());
    }

    public long getCurrentOrganizationCount() {
        return currentOrganizationCount;
    }

    public boolean isActiveAndValid() {
        return subscription.allowsAccess();
    }

    public boolean isTrial() {
        return SubscriptionConstants.SubscriptionStatus.TRIAL.equals(subscription.getStatus());
    }

    public boolean isCancelled() {
        return SubscriptionConstants.SubscriptionStatus.CANCELLED.equals(subscription.getStatus());
    }

    public boolean isExpired() {
        return SubscriptionConstants.SubscriptionStatus.EXPIRED.equals(subscription.getStatus()) ||
                (subscription.getEndedAt() != null && subscription.getEndedAt().isBefore(OffsetDateTime.now()));
    }

    public boolean isSuspended() {
        return SubscriptionConstants.SubscriptionStatus.SUSPENDED.equals(subscription.getStatus());
    }

    public Long getDaysRemaining() {
        if (subscription.getEndedAt() == null) {
            return null;
        }
        OffsetDateTime now = OffsetDateTime.now();
        if (subscription.getEndedAt().isBefore(now)) {
            return 0L;
        }
        return java.time.Duration.between(now, subscription.getEndedAt()).toDays();
    }
}
