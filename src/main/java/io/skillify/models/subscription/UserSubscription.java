package io.skillify.models.subscription;

import java.time.OffsetDateTime;
import java.util.UUID;

import org.hibernate.annotations.UuidGenerator;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import io.skillify.constants.SubscriptionConstants;
import io.skillify.models.user.User;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EntityListeners;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.Index;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Entity
@Table(name = "user_subscriptions",
    indexes = {
        @Index(name = "idx_user_subscriptions_user_id", columnList = "user_id"),
        @Index(name = "idx_user_subscriptions_status", columnList = "status"),
        @Index(name = "idx_user_subscriptions_plan_id", columnList = "plan_id"),
        @Index(name = "idx_user_subscriptions_dates", columnList = "started_at, ended_at"),
        @Index(name = "idx_user_subscriptions_stripe_subscription", columnList = "stripe_subscription_id"),
        @Index(name = "idx_user_subscriptions_stripe_customer", columnList = "stripe_customer_id")
    }
)
@EntityListeners(AuditingEntityListener.class)
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserSubscription {

    @Id
    @UuidGenerator(style = UuidGenerator.Style.RANDOM)
    @Column(name = "subscription_id")
    private UUID id;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id", nullable = false)
    private User user;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "plan_id", nullable = false)
    private SubscriptionPlan plan;

    @NotNull
    @Column(nullable = false)
    @Builder.Default
    private String status = SubscriptionConstants.SubscriptionStatus.ACTIVE;

    @NotNull
    @Column(name = "started_at", nullable = false, columnDefinition = "TIMESTAMP WITH TIME ZONE")
    @Builder.Default
    private OffsetDateTime startedAt = OffsetDateTime.now();

    @Column(name = "ended_at", columnDefinition = "TIMESTAMP WITH TIME ZONE")
    private OffsetDateTime endedAt;

    @Column(name = "stripe_subscription_id", unique = true)
    private String stripeSubscriptionId;

    @Column(name = "stripe_customer_id")
    private String stripeCustomerId;

    @Column(name = "current_period_start", columnDefinition = "TIMESTAMP WITH TIME ZONE")
    private OffsetDateTime currentPeriodStart;

    @Column(name = "current_period_end", columnDefinition = "TIMESTAMP WITH TIME ZONE")
    private OffsetDateTime currentPeriodEnd;

    @Column(name = "trial_end", columnDefinition = "TIMESTAMP WITH TIME ZONE")
    private OffsetDateTime trialEnd;

    @Column(name = "payment_method_id")
    private String paymentMethodId;

    @CreatedDate
    @Column(name = "created_at", nullable = false, columnDefinition = "TIMESTAMP WITH TIME ZONE")
    private OffsetDateTime createdAt;

    @LastModifiedDate
    @Column(name = "updated_at", nullable = false, columnDefinition = "TIMESTAMP WITH TIME ZONE")
    private OffsetDateTime updatedAt;

    public boolean isActive() {
        return SubscriptionConstants.SubscriptionStatus.ACTIVE.equals(status) ||
                SubscriptionConstants.SubscriptionStatus.TRIAL.equals(status);
    }

    public boolean allowsAccess() {
        return isActive() && (endedAt == null || endedAt.isAfter(OffsetDateTime.now()));
    }

    public void markAsEnded() {
        if (this.endedAt == null) {
            this.endedAt = OffsetDateTime.now();
        }
    }

    public boolean isValidStatus() {
        return status != null && (
            status.equals(SubscriptionConstants.SubscriptionStatus.ACTIVE) ||
            status.equals(SubscriptionConstants.SubscriptionStatus.CANCELLED) ||
            status.equals(SubscriptionConstants.SubscriptionStatus.EXPIRED) ||
            status.equals(SubscriptionConstants.SubscriptionStatus.SUSPENDED) ||
            status.equals(SubscriptionConstants.SubscriptionStatus.TRIAL)
        );
    }

    public boolean isStripeManaged() {
        return stripeSubscriptionId != null;
    }

    public boolean isInTrial() {
        return trialEnd != null && trialEnd.isAfter(OffsetDateTime.now());
    }

    public boolean requiresPayment() {
        return plan.requiresPayment() && !isInTrial();
    }

    public boolean isCurrentPeriodActive() {
        if (currentPeriodStart == null || currentPeriodEnd == null) {
            return false;
        }
        var now = OffsetDateTime.now();
        return !now.isBefore(currentPeriodStart) && !now.isAfter(currentPeriodEnd);
    }
}
