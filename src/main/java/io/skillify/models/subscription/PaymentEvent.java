package io.skillify.models.subscription;

import java.time.OffsetDateTime;
import java.util.UUID;

import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.annotations.UuidGenerator;
import org.hibernate.type.SqlTypes;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import com.fasterxml.jackson.databind.JsonNode;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EntityListeners;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.Index;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Entity
@Table(name = "payment_events",
    indexes = {
        @Index(name = "idx_payment_events_stripe_id", columnList = "stripe_event_id"),
        @Index(name = "idx_payment_events_processed", columnList = "processed"),
        @Index(name = "idx_payment_events_created", columnList = "created_at"),
        @Index(name = "idx_payment_events_type", columnList = "event_type"),
        @Index(name = "idx_payment_events_retry", columnList = "retry_count")
    }
)
@EntityListeners(AuditingEntityListener.class)
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PaymentEvent {

    @Id
    @UuidGenerator(style = UuidGenerator.Style.RANDOM)
    @Column(name = "event_id")
    private UUID id;

    @NotNull
    @Column(name = "stripe_event_id", nullable = false, unique = true)
    private String stripeEventId;

    @NotNull
    @Column(name = "event_type", nullable = false)
    private String eventType;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "subscription_id")
    private UserSubscription subscription;

    @Builder.Default
    private boolean processed = false;

    @Column(name = "processed_at", columnDefinition = "TIMESTAMP WITH TIME ZONE")
    private OffsetDateTime processedAt;

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "raw_data", columnDefinition = "jsonb")
    private JsonNode rawData;

    @Column(name = "error_message")
    private String errorMessage;

    @Builder.Default
    @Column(name = "retry_count")
    private Integer retryCount = 0;

    @CreatedDate
    @Column(name = "created_at", nullable = false, columnDefinition = "TIMESTAMP WITH TIME ZONE")
    private OffsetDateTime createdAt;

    public void markAsProcessed() {
        this.processed = true;
        this.processedAt = OffsetDateTime.now();
    }

    public void incrementRetryCount() {
        this.retryCount = (this.retryCount == null) ? 1 : this.retryCount + 1;
    }

    public void recordError(String error) {
        this.errorMessage = error;
        incrementRetryCount();
    }

    public boolean canRetry() {
        return retryCount < 3; // Max 3 retries
    }

    public boolean isProcessed() {
        return processed;
    }
}
