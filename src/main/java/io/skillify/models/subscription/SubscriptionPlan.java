package io.skillify.models.subscription;

import java.time.OffsetDateTime;
import java.util.UUID;

import org.hibernate.annotations.UuidGenerator;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EntityListeners;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Entity
@Table(name = "subscription_plans")
@EntityListeners(AuditingEntityListener.class)
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SubscriptionPlan {

    @Id
    @UuidGenerator(style = UuidGenerator.Style.RANDOM)
    @Column(name = "plan_id", updatable = false, nullable = false)
    private UUID id;

    @Column(nullable = false, unique = true)
    private String name;

    @Column(name = "display_name", nullable = false)
    private String displayName;

    @Column(name = "max_organizations", nullable = false)
    private Integer maxOrganizations;

    @Column(name = "is_active", nullable = false)
    @Builder.Default
    private Boolean isActive = true;

    @Column(name = "price_monthly_cents")
    private Integer priceMonthlyCents;

    @Column(name = "price_yearly_cents")
    private Integer priceYearlyCents;

    @Column(name = "stripe_product_id")
    private String stripeProductId;

    @Column(name = "stripe_price_id")
    private String stripePriceId;

    @CreatedDate
    @Column(name = "created_at", nullable = false)
    private OffsetDateTime createdAt;

    public boolean isFree() {
        return priceMonthlyCents == null || priceMonthlyCents == 0;
    }

    public boolean requiresPayment() {
        return !isFree();
    }

    public String getFormattedPrice() {
        if (isFree()) {
            return "Free";
        }
        return String.format("$%.2f", priceMonthlyCents / 100.0);
    }

    public boolean isStripeConfigured() {
        return stripeProductId != null && (isFree() || stripePriceId != null);
    }
}
