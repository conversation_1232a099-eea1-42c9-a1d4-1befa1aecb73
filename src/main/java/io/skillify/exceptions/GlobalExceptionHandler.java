package io.skillify.exceptions;

import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authorization.AuthorizationDeniedException;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import com.stripe.exception.AuthenticationException;
import com.stripe.exception.CardException;
import com.stripe.exception.IdempotencyException;
import com.stripe.exception.InvalidRequestException;
import com.stripe.exception.PermissionException;
import com.stripe.exception.RateLimitException;
import com.stripe.exception.SignatureVerificationException;
import com.stripe.exception.StripeException;

import io.skillify.dtos.ResponseDto;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RestControllerAdvice
public class GlobalExceptionHandler {

    @ExceptionHandler(MethodArgumentNotValidException.class)
    public ResponseEntity<ResponseDto<?>> handleValidationExceptions(MethodArgumentNotValidException ex) {
        Map<String, String> errors = new HashMap<>();
        for (FieldError error : ex.getBindingResult().getFieldErrors()) {
            errors.put(error.getField(), error.getDefaultMessage());
        }
        String combinedErrors = errors.entrySet().stream()
                .map(e -> e.getKey() + ": " + e.getValue())
                .collect(Collectors.joining(", "));
        return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                .body(ResponseDto.error(combinedErrors, HttpStatus.BAD_REQUEST));
    }

    @ExceptionHandler(BadRequestException.class)
    public ResponseEntity<ResponseDto<?>> handleBadRequestException(BadRequestException ex) {
        log.warn("Bad request: {}", ex.getMessage());
        return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                .body(ResponseDto.error(ex.getMessage(), HttpStatus.BAD_REQUEST));
    }

    @ExceptionHandler(ResourceNotFoundException.class)
    public ResponseEntity<ResponseDto<?>> handleResourceNotFoundException(ResourceNotFoundException ex) {
        log.warn("Resource not found: {}", ex.getMessage());
        return ResponseEntity.status(HttpStatus.NOT_FOUND)
                .body(ResponseDto.error(ex.getMessage(), HttpStatus.NOT_FOUND));
    }

    @ExceptionHandler(ResourceAlreadyExistsException.class)
    public ResponseEntity<ResponseDto<?>> handleResourceAlreadyExistsException(ResourceAlreadyExistsException ex) {
        log.warn("Resource already exists: {}", ex.getMessage());
        return ResponseEntity.status(HttpStatus.CONFLICT)
                .body(ResponseDto.error(ex.getMessage(), HttpStatus.CONFLICT));
    }

    @ExceptionHandler(OperationNotPermittedException.class)
    public ResponseEntity<ResponseDto<?>> handleOperationNotPermittedException(OperationNotPermittedException ex) {
        log.warn("Operation not permitted: {}", ex.getMessage());
        return ResponseEntity.status(HttpStatus.FORBIDDEN)
                .body(ResponseDto.error(ex.getMessage(), HttpStatus.FORBIDDEN));
    }

    @ExceptionHandler(SubscriptionLimitException.class)
    public ResponseEntity<ResponseDto<?>> handleSubscriptionLimitException(SubscriptionLimitException ex) {
        log.warn("Subscription limit exceeded: {}", ex.getMessage());
        return ResponseEntity.status(HttpStatus.PAYMENT_REQUIRED)
                .body(ResponseDto.error(ex.getMessage(), HttpStatus.PAYMENT_REQUIRED));
    }

    @ExceptionHandler(InvalidCredentialsException.class)
    public ResponseEntity<ResponseDto<?>> handleInvalidCredentialsException(InvalidCredentialsException ex) {
        log.warn("Invalid credentials: {}", ex.getMessage());
        return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                .body(ResponseDto.error(ex.getMessage(), HttpStatus.UNAUTHORIZED));
    }

    @ExceptionHandler(BadCredentialsException.class)
    public ResponseEntity<ResponseDto<?>> handleBadCredentialsException(BadCredentialsException ex) {
        log.warn("Authentication failed: {}", ex.getMessage());
        return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                .body(ResponseDto.error("Invalid username or password", HttpStatus.UNAUTHORIZED));
    }

    // Spring Security Exception Handlers
    @ExceptionHandler(AuthorizationDeniedException.class)
    public ResponseEntity<ResponseDto<?>> handleAuthorizationDeniedException(AuthorizationDeniedException ex) {
        log.warn("Authorization denied: {}", ex.getMessage());
        return ResponseEntity.status(HttpStatus.FORBIDDEN)
                .body(ResponseDto.error("You don't have permission to access this resource. Please ensure you are logged in.", HttpStatus.FORBIDDEN));
    }

    @ExceptionHandler(AccessDeniedException.class)
    public ResponseEntity<ResponseDto<?>> handleAccessDeniedException(AccessDeniedException ex) {
        log.warn("Access denied: {}", ex.getMessage());
        return ResponseEntity.status(HttpStatus.FORBIDDEN)
                .body(ResponseDto.error("Access denied. You don't have the required permissions for this operation.", HttpStatus.FORBIDDEN));
    }

    // Database Exception Handlers
    @ExceptionHandler(DataIntegrityViolationException.class)
    public ResponseEntity<ResponseDto<?>> handleDataIntegrityViolationException(DataIntegrityViolationException ex) {
        log.warn("Data integrity violation: {}", ex.getMessage());

        String message = "A data constraint was violated. This might be due to duplicate data or invalid references.";

        // Provide more specific messages for common constraint violations
        if (ex.getMessage().contains("duplicate key") || ex.getMessage().contains("unique constraint")) {
            message = "This record already exists. Please check for duplicates and try again.";
        } else if (ex.getMessage().contains("foreign key") || ex.getMessage().contains("referential integrity")) {
            message = "Invalid reference to related data. Please check your input and try again.";
        }

        return ResponseEntity.status(HttpStatus.CONFLICT)
                .body(ResponseDto.error(message, HttpStatus.CONFLICT));
    }

    // Stripe Exception Handlers
    @ExceptionHandler(CardException.class)
    public ResponseEntity<ResponseDto<?>> handleCardException(CardException ex) {
        log.warn("Card declined: {} - {}", ex.getCode(), ex.getMessage());
        String userMessage = switch (ex.getCode()) {
            case "card_declined" -> "Your card was declined. Please try a different payment method.";
            case "expired_card" -> "Your card has expired. Please use a different card.";
            case "insufficient_funds" -> "Your card has insufficient funds. Please use a different card.";
            case "incorrect_cvc" -> "Your card's security code is incorrect. Please check and try again.";
            case "processing_error" -> "An error occurred processing your card. Please try again.";
            default -> "There was an issue with your payment method. Please try again.";
        };
        return ResponseEntity.status(HttpStatus.PAYMENT_REQUIRED)
                .body(ResponseDto.error(userMessage, HttpStatus.PAYMENT_REQUIRED));
    }

    @ExceptionHandler(RateLimitException.class)
    public ResponseEntity<ResponseDto<?>> handleRateLimitException(RateLimitException ex) {
        log.error("Stripe rate limit exceeded: {}", ex.getMessage());
        return ResponseEntity.status(HttpStatus.TOO_MANY_REQUESTS)
                .body(ResponseDto.error("Too many requests. Please try again later.", HttpStatus.TOO_MANY_REQUESTS));
    }

    @ExceptionHandler(InvalidRequestException.class)
    public ResponseEntity<ResponseDto<?>> handleInvalidRequestException(InvalidRequestException ex) {
        log.error("Invalid Stripe request: {} - {}", ex.getParam(), ex.getMessage());
        return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                .body(ResponseDto.error("Invalid payment request. Please check your information and try again.",
                        HttpStatus.BAD_REQUEST));
    }

    @ExceptionHandler(AuthenticationException.class)
    public ResponseEntity<ResponseDto<?>> handleStripeAuthenticationException(AuthenticationException ex) {
        log.error("Stripe authentication failed: {}", ex.getMessage());
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ResponseDto.error("Payment service configuration error. Please contact support.",
                        HttpStatus.INTERNAL_SERVER_ERROR));
    }

    @ExceptionHandler(PermissionException.class)
    public ResponseEntity<ResponseDto<?>> handlePermissionException(PermissionException ex) {
        log.error("Stripe permission denied: {}", ex.getMessage());
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ResponseDto.error("Payment service permission error. Please contact support.",
                        HttpStatus.INTERNAL_SERVER_ERROR));
    }

    @ExceptionHandler(IdempotencyException.class)
    public ResponseEntity<ResponseDto<?>> handleIdempotencyException(IdempotencyException ex) {
        log.warn("Stripe idempotency conflict: {}", ex.getMessage());
        return ResponseEntity.status(HttpStatus.CONFLICT)
                .body(ResponseDto.error("Duplicate request detected. Please try again.", HttpStatus.CONFLICT));
    }

    @ExceptionHandler(SignatureVerificationException.class)
    public ResponseEntity<ResponseDto<?>> handleSignatureVerificationException(SignatureVerificationException ex) {
        log.error("Webhook signature verification failed: {}", ex.getMessage());
        return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                .body(ResponseDto.error("Invalid webhook signature", HttpStatus.BAD_REQUEST));
    }

    @ExceptionHandler(StripeException.class)
    public ResponseEntity<ResponseDto<?>> handleGenericStripeException(StripeException ex) {
        log.error("Stripe error: {} - {}", ex.getCode(), ex.getMessage(), ex);
        String userMessage = ex.getUserMessage() != null ? ex.getUserMessage()
                : "Payment processing error. Please try again or contact support.";
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ResponseDto.error(userMessage, HttpStatus.INTERNAL_SERVER_ERROR));
    }

    @ExceptionHandler(Exception.class)
    public ResponseEntity<ResponseDto<?>> handleException(Exception ex) {
        log.error("Unexpected error: {}", ex.getMessage(), ex);
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ResponseDto.error("An unexpected error occurred. Please try again.",
                        HttpStatus.INTERNAL_SERVER_ERROR));
    }
}
