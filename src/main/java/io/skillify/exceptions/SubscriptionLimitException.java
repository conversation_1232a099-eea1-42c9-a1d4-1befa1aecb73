package io.skillify.exceptions;

import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

import io.skillify.constants.SubscriptionConstants;

@ResponseStatus(HttpStatus.PAYMENT_REQUIRED)
public class SubscriptionLimitException extends RuntimeException {

    private final long currentCount;
    private final int maxAllowed;
    private final String planName;

    public SubscriptionLimitException(long currentCount, int maxAllowed, String planName) {
        super(String.format(SubscriptionConstants.ErrorMessages.ORGANIZATION_LIMIT_REACHED,
                currentCount, maxAllowed));
        this.currentCount = currentCount;
        this.maxAllowed = maxAllowed;
        this.planName = planName;
    }

    public SubscriptionLimitException(String message) {
        super(message);
        this.currentCount = 0;
        this.maxAllowed = 0;
        this.planName = "unknown";
    }

    public SubscriptionLimitException(String message, Throwable cause) {
        super(message, cause);
        this.currentCount = 0;
        this.maxAllowed = 0;
        this.planName = "unknown";
    }

    public long getCurrentCount() {
        return currentCount;
    }

    public int getMaxAllowed() {
        return maxAllowed;
    }

    public String getPlanName() {
        return planName;
    }
}
