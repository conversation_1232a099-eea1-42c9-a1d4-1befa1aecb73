package io.skillify.config.security;

import java.util.List;

public final class SecurityConstants {
    private SecurityConstants() {}

    public static final String[] PUBLIC_PATHS = {
            "/login",
            "/register",
            "/v3/api-docs/**",
            "/swagger-ui/**",
            "/swagger-ui.html",
            "/webjars/**",
    };

    public static final String[] ADMIN_PATHS = {
    };

    public static final List<String> ALLOWED_ORIGINS = List.of(
        "http://localhost:8080",
        "http://localhost:3000",
        "http://localhost:3001"
    );

    public static final List<String> ALLOWED_METHODS = List.of(
        "GET", "POST", "PUT", "PATCH", "DELETE", "OPTIONS"
    );

    public static final List<String> EXPOSED_HEADERS = List.of(
        "Authorization",
        "Content-Type",
        "Accept",
        "X-Requested-With",
        "Cache-Control"
    );
}
