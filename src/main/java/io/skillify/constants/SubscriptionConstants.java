package io.skillify.constants;

public final class SubscriptionConstants {

    private SubscriptionConstants() {
    }

    public static final class PlanNames {
        public static final String FREE = "free";
        public static final String PRO = "pro";
        public static final String ENTERPRISE = "enterprise";
    }

    public static final class SubscriptionStatus {
        public static final String ACTIVE = "active";
        public static final String CANCELLED = "cancelled";
        public static final String EXPIRED = "expired";
        public static final String SUSPENDED = "suspended";
        public static final String TRIAL = "trial";
    }

    public static final class ErrorMessages {
        public static final String FREE_PLAN_NOT_FOUND = "Free plan not found in system";
        public static final String ORGANIZATION_LIMIT_REACHED = "You have reached your organization limit (%d/%d). Upgrade your plan to create more organizations.";
        public static final String INVALID_SUBSCRIPTION_STATUS = "Invalid subscription status. Allowed values: active, cancelled, expired, suspended, trial";
        public static final String SUBSCRIPTION_NOT_FOUND = "User subscription not found";
        public static final String MULTIPLE_ACTIVE_SUBSCRIPTIONS = "User cannot have multiple active subscriptions";
        public static final String TRIAL_ALREADY_USED = "Trial has already been used for this plan";
        public static final String ACTIVE_TRIAL_EXISTS = "User already has an active trial subscription";
        public static final String TRIAL_NOT_AVAILABLE_FOR_FREE = "Trial is not available for free plan";
    }
}
