package io.skillify.dtos.payment;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

public class PaymentDto {

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(name = "CreateCheckoutSessionRequest", description = "Request to create Stripe checkout session")
    public static class CreateCheckoutSessionRequest {

        @NotBlank(message = "Plan name is required")
        @Schema(description = "Plan name to subscribe to", example = "pro", required = true)
        private String planName;

        @Schema(description = "Return URL after successful payment", example = "https://app.skillify.io/dashboard")
        private String returnUrl;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(name = "CheckoutSessionResponse", description = "Stripe checkout session details")
    public static class CheckoutSessionResponse {

        @Schema(description = "Stripe checkout session ID", example = "cs_test_123456789")
        private String sessionId;

        @Schema(description = "Stripe checkout URL", example = "https://checkout.stripe.com/pay/cs_test_123456789")
        private String url;

        @Schema(description = "Session expiration timestamp", example = "1640995200")
        private Long expiresAt;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(name = "CheckoutSuccessRequest", description = "Checkout success verification")
    public static class CheckoutSuccessRequest {

        @NotBlank(message = "Session ID is required")
        @Schema(description = "Stripe checkout session ID", example = "cs_test_123456789", required = true)
        private String sessionId;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(name = "CheckoutSuccessResponse", description = "Checkout success confirmation")
    public static class CheckoutSuccessResponse {

        @Schema(description = "Whether checkout was successful", example = "true")
        private Boolean success;

        @Schema(description = "Stripe checkout session ID", example = "cs_test_123456789")
        private String sessionId;

        @Schema(description = "Stripe checkout URL", example = "https://checkout.stripe.com/pay/cs_test_123456789")
        private String url;

        @Schema(description = "Subscription ID", example = "550e8400-e29b-41d4-a716-446655440000")
        private String subscriptionId;

        @Schema(description = "Plan name", example = "pro")
        private String planName;

        @Schema(description = "Success message", example = "Welcome to Skillify Pro!")
        private String message;

        @Schema(description = "When subscription started", example = "2025-01-01T00:00:00Z")
        private String startedAt;

        @Schema(description = "Current billing period end", example = "2025-02-01T00:00:00Z")
        private String currentPeriodEnd;

        @Schema(description = "Trial end date (null if not in trial)", example = "2025-01-15T00:00:00Z")
        private String trialEnd;

        @Schema(description = "Session expiration timestamp", example = "1640995200")
        private Long expiresAt;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(name = "CancelSubscriptionRequest", description = "Request to cancel subscription")
    public static class CancelSubscriptionRequest {

        @Schema(description = "Whether to cancel immediately or at period end", example = "false")
        @Builder.Default
        private Boolean immediate = false;

        @Schema(description = "Reason for cancellation", example = "No longer needed")
        private String reason;

        @Schema(description = "Feedback for improvement", example = "Too expensive")
        private String feedback;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(name = "CancelSubscriptionResponse", description = "Response after successful subscription cancellation")
    public static class CancelSubscriptionResponse {

        @Schema(description = "Success message", example = "Subscription cancelled successfully")
        private String message;

        @Schema(description = "Cancelled subscription ID", example = "550e8400-e29b-41d4-a716-446655440000")
        private String subscriptionId;

        @Schema(description = "Plan name that was cancelled", example = "pro")
        private String planName;

        @Schema(description = "Subscription status after cancellation", example = "cancelled")
        private String status;

        @Schema(description = "Whether cancellation was immediate", example = "false")
        private Boolean immediate;

        @Schema(description = "When subscription will end (if not immediate)", example = "2024-02-15T10:30:00Z")
        private String endsAt;

        @Schema(description = "Free plan subscription ID if created", example = "550e8400-e29b-41d4-a716-446655440001")
        private String freePlanSubscriptionId;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(name = "UpdatePaymentMethodRequest", description = "Request to update payment method")
    public static class UpdatePaymentMethodRequest {

        @NotBlank(message = "Payment method ID is required")
        @Schema(description = "Stripe payment method ID", example = "pm_1234567890", required = true)
        private String paymentMethodId;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(name = "PaymentMethodResponse", description = "Payment method details")
    public static class PaymentMethodResponse {

        @Schema(description = "Payment method ID", example = "pm_1234567890")
        private String id;

        @Schema(description = "Payment method type", example = "card")
        private String type;

        @Schema(description = "Card brand", example = "visa")
        private String brand;

        @Schema(description = "Last 4 digits", example = "4242")
        private String last4;

        @Schema(description = "Expiry month", example = "12")
        private Integer expMonth;

        @Schema(description = "Expiry year", example = "2025")
        private Integer expYear;
    }



    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(name = "PaymentEventResponse", description = "Payment event details for admin/debugging")
    public static class PaymentEventResponse {

        @Schema(description = "Event ID", example = "550e8400-e29b-41d4-a716-446655440000")
        private String id;

        @Schema(description = "Stripe event ID", example = "evt_1234567890")
        private String stripeEventId;

        @Schema(description = "Event type", example = "customer.subscription.created")
        private String eventType;

        @Schema(description = "Whether event was processed", example = "true")
        private Boolean processed;

        @Schema(description = "When event was created", example = "2024-01-15T10:30:00Z")
        private String createdAt;

        @Schema(description = "When event was processed", example = "2024-01-15T10:30:05Z")
        private String processedAt;

        @Schema(description = "Error message if processing failed", example = "Invalid subscription data")
        private String errorMessage;
    }
}
