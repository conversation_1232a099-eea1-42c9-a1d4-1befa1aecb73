package io.skillify.dtos.test;

import java.time.OffsetDateTime;
import java.util.UUID;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.skillify.models.test.Test.TestStatus;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

public class TestDto {

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Schema(name = "TestCreateRequest", description = "Test creation request")
    public static class CreateRequest {

        @NotBlank(message = "Name is required")
        @Size(min = 3, max = 255, message = "Name must be between 3 and 255 characters")
        @Schema(description = "Test name", example = "Java Developer Assessment")
        private String name;

        @Size(max = 5000, message = "Description must not exceed 5000 characters")
        @Schema(description = "Test description", example = "Assessment for Java Developer position")
        private String description;

        @Schema(description = "Time limit in minutes", example = "60")
        private Integer timeLimit;

        @Schema(description = "Start time", example = "2025-01-01T09:00:00Z")
        private OffsetDateTime startTime;

        @Schema(description = "End time", example = "2025-01-31T18:00:00Z")
        private OffsetDateTime endTime;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Schema(name = "TestUpdateRequest", description = "Test update request")
    public static class UpdateRequest {

        @NotBlank(message = "Name is required")
        @Size(min = 3, max = 255, message = "Name must be between 3 and 255 characters")
        @Schema(description = "Test name", example = "Java Developer Assessment")
        private String name;

        @Size(max = 5000, message = "Description must not exceed 5000 characters")
        @Schema(description = "Test description", example = "Assessment for Java Developer position")
        private String description;

        @Schema(description = "Time limit in minutes", example = "60")
        private Integer timeLimit;

        @Schema(description = "Start time", example = "2025-01-01T09:00:00Z")
        private OffsetDateTime startTime;

        @Schema(description = "End time", example = "2025-01-31T18:00:00Z")
        private OffsetDateTime endTime;

        @Schema(description = "Test status", example = "PUBLISHED")
        private TestStatus status;
    }

    @Data
    @SuperBuilder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Schema(name = "TestResponse", description = "Test response")
    public static class Response {

        @Schema(description = "Test ID", example = "b1c2d3e4-f5a6-7890-1234-567890abcdef")
        private UUID id;

        @Schema(description = "Test name", example = "Java Developer Assessment")
        private String name;

        @Schema(description = "Test description", example = "Assessment for Java Developer position")
        private String description;

        @Schema(description = "Time limit in minutes", example = "60")
        private Integer timeLimit;

        @Schema(description = "Job ID", example = "a1b2c3d4-e5f6-7890-1234-567890abcdef")
        private UUID jobId;

        @Schema(description = "Job title", example = "Senior Java Developer")
        private String jobTitle;

        @Schema(description = "Start time", example = "2025-01-01T09:00:00Z")
        private OffsetDateTime startTime;

        @Schema(description = "End time", example = "2025-01-31T18:00:00Z")
        private OffsetDateTime endTime;

        @Schema(description = "Test status", example = "PUBLISHED")
        private TestStatus status;

        @Schema(description = "Created by user ID", example = "c1d2e3f4-a5b6-7890-1234-567890abcdef")
        private String createdBy;

        @Schema(description = "Updated by user ID", example = "c1d2e3f4-a5b6-7890-1234-567890abcdef")
        private String updatedBy;

        @Schema(description = "Creation timestamp", example = "2025-01-01T09:00:00Z")
        private OffsetDateTime createdAt;

        @Schema(description = "Last update timestamp", example = "2025-01-01T09:00:00Z")
        private OffsetDateTime updatedAt;
    }
}
