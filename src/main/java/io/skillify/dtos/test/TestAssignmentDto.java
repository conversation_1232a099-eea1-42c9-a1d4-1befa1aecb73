package io.skillify.dtos.test;

import java.time.OffsetDateTime;
import java.util.List;
import java.util.UUID;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.skillify.models.test.TestAssignmentStatus;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

public class TestAssignmentDto {
    @Data
    @SuperBuilder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Schema(name = "TestAssignmentResponse", description = "Test assignment response")
    public static class Response {
        private UUID id;
        private UUID testId;
        private String candidateEmail;
        private TestAssignmentStatus status;
        private OffsetDateTime startTime;
        private OffsetDateTime endTime;
        private Float score;
        private OffsetDateTime createdAt;
        private OffsetDateTime updatedAt;
        private String createdBy;
        private String updatedBy;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Schema(name = "AssignmentDetail", description = "Details for a single assignment, requiring candidate email.")
    public static class AssignmentDetail {
        @NotBlank(message = "Candidate email must not be blank")
        @Email(message = "Candidate email must be a valid email address")
        private String email;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Schema(name = "TestAssignmentRequest", description = "Request to create test assignments for a single test.")
    public static class CreateTestAssignmentRequest {
        @NotNull(message = "Test ID is required")
        private UUID testId;

        @NotEmpty(message = "Assignments list cannot be empty")
        private List<@Valid AssignmentDetail> assignments;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Schema(name = "UpdateTestAssignmentRequest", description = "Update test assignment request")
    public static class UpdateTestAssignmentRequest {
        @NotNull(message = "Assignment status is required")
        private TestAssignmentStatus status;
    }
}
