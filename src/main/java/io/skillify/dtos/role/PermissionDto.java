package io.skillify.dtos.role;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;

@Schema(description = "DTO for Permission details")
public record PermissionDto(
    @Schema(description = "Unique name of the permission", example = "USER_READ", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "Permission name cannot be blank")
    String permissionName,

    @Schema(description = "Detailed description of what the permission allows", example = "Allows reading user account details")
    String description,

    @Schema(description = "Category of the permission, e.g., ADMINISTRATION, USER", example = "ADMINISTRATION", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "Permission category cannot be blank")
    String category
) {
}
