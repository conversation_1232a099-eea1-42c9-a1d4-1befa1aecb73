package io.skillify.dtos.subscription;

import java.time.OffsetDateTime;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

public class SubscriptionDto {

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(name = "SubscriptionStatusResponse", description = "User's subscription status and limits")
    public static class StatusResponse {

        @Schema(description = "Plan name", example = "free")
        private String planName;

        @Schema(description = "Plan display name", example = "Free Plan")
        private String displayName;

        @Schema(description = "Subscription status", example = "active")
        private String status;

        @Schema(description = "When subscription started", example = "2025-01-01T00:00:00Z")
        private OffsetDateTime startedAt;

        @Schema(description = "When subscription ends (null for ongoing)", example = "2025-12-31T23:59:59Z")
        private OffsetDateTime endedAt;

        @Schema(description = "Current billing period start", example = "2025-01-01T00:00:00Z")
        private OffsetDateTime currentPeriodStart;

        @Schema(description = "Current billing period end", example = "2025-02-01T00:00:00Z")
        private OffsetDateTime currentPeriodEnd;

        @Schema(description = "Trial end date (null if not in trial)", example = "2025-01-15T00:00:00Z")
        private OffsetDateTime trialEnd;

        @Schema(description = "Current number of organizations", example = "1")
        private long currentOrganizations;

        @Schema(description = "Maximum organizations allowed", example = "1")
        private int maxOrganizations;

        @Schema(description = "Whether user has reached the limit", example = "true")
        private boolean hasReachedLimit;

        @Schema(description = "Whether user can create more organizations", example = "false")
        private boolean canCreateOrganization;

        @Schema(description = "Usage percentage", example = "100")
        private int usagePercentage;

        @Schema(description = "Whether subscription is in trial period", example = "false")
        private boolean isTrial;

        @Schema(description = "Whether subscription is active and valid", example = "true")
        private boolean isActiveAndValid;

        @Schema(description = "Days remaining until expiration (null if no end date)", example = "30")
        private Long daysRemaining;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(name = "UpdateSubscriptionRequest", description = "Request to update user subscription")
    public static class UpdateRequest {

        @NotBlank(message = "Plan name is required")
        @Schema(description = "New plan name", example = "pro", required = true)
        private String planName;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(name = "CancelSubscriptionRequest", description = "Request to cancel subscription")
    public static class CancelRequest {

        @Schema(description = "Whether to cancel immediately or at period end", example = "false")
        @Builder.Default
        private boolean immediate = false;

        @Schema(description = "Reason for cancellation", example = "No longer needed")
        private String reason;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(name = "SubscriptionResponse", description = "Full subscription details")
    public static class Response {

        @Schema(description = "Subscription ID")
        private String id;

        @Schema(description = "Plan details")
        private PlanResponse plan;

        @Schema(description = "Subscription status", example = "active")
        private String status;

        @Schema(description = "When subscription started")
        private OffsetDateTime startedAt;

        @Schema(description = "When subscription ends")
        private OffsetDateTime endedAt;

        @Schema(description = "Current billing period start")
        private OffsetDateTime currentPeriodStart;

        @Schema(description = "Current billing period end")
        private OffsetDateTime currentPeriodEnd;

        @Schema(description = "Trial end date (null if not in trial)")
        private OffsetDateTime trialEnd;

        @Schema(description = "When subscription was created")
        private OffsetDateTime createdAt;

        @Schema(description = "When subscription was last updated")
        private OffsetDateTime updatedAt;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(name = "PlanResponse", description = "Subscription plan details")
    public static class PlanResponse {

        @Schema(description = "Plan ID")
        private String id;

        @Schema(description = "Plan name", example = "free")
        private String name;

        @Schema(description = "Plan display name", example = "Free Plan")
        private String displayName;

        @Schema(description = "Maximum organizations allowed", example = "1")
        private int maxOrganizations;

        @Schema(description = "Whether plan is active", example = "true")
        private boolean isActive;
    }
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(name = "AvailablePlanResponse", description = "Available plan with user-specific restrictions")
    public static class AvailablePlanResponse {

        @Schema(description = "Plan details")
        private PlanResponse plan;

        @Schema(description = "Whether user can start a trial for this plan", example = "true")
        private boolean canStartTrial;

        @Schema(description = "Whether user has used trial for this plan before", example = "false")
        private boolean hasUsedTrial;

        @Schema(description = "Whether this is the user's current plan", example = "false")
        private boolean isCurrentPlan;

        @Schema(description = "Restriction reason if plan is not fully available")
        private String restrictionReason;
    }
}
