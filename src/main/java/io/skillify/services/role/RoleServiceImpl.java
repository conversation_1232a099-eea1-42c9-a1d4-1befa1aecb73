package io.skillify.services.role;

import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import io.skillify.dtos.role.PermissionDto;
import io.skillify.dtos.role.RoleDto;
import io.skillify.exceptions.BadRequestException;
import io.skillify.exceptions.ResourceAlreadyExistsException;
import io.skillify.exceptions.ResourceNotFoundException;
import io.skillify.mappers.PermissionMapper;
import io.skillify.mappers.RoleMapper;
import io.skillify.models.user.Permission;
import io.skillify.models.user.Role;
import io.skillify.repositories.user.PermissionRepository;
import io.skillify.repositories.user.RoleRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Service
@RequiredArgsConstructor
@Slf4j
public class RoleServiceImpl implements RoleService {

    private final RoleRepository roleRepository;
    private final PermissionRepository permissionRepository;
    private final RoleMapper roleMapper;
    private final PermissionMapper permissionMapper;

    @Override
    @Transactional
    public RoleDto.RoleResponseDto createRole(RoleDto.RoleCreateRequestDto createRequestDto) {
        log.info("Attempting to create role with name: {}", createRequestDto.name());
        Role.RoleType roleType = convertRoleNameToEnum(createRequestDto.name());

        if (roleRepository.findByName(roleType).isPresent()) {
            log.warn("Role with name '{}' already exists.", roleType.name());
            throw new ResourceAlreadyExistsException("Role with name '" + roleType.name() + "' already exists.");
        }

        Set<Permission> assignedPermissions = new HashSet<>();
        if (createRequestDto.permissionNames() != null && !createRequestDto.permissionNames().isEmpty()) {
            assignedPermissions = permissionRepository.findByPermissionNameIn(createRequestDto.permissionNames());
            // Check if all permissions were found
            if (assignedPermissions.size() != createRequestDto.permissionNames().size()) {
                log.warn("One or more permissions not found for role creation: {}", createRequestDto.permissionNames());
                String foundPermissionNames = assignedPermissions.stream().map(Permission::getPermissionName).collect(Collectors.joining(", "));
                throw new ResourceNotFoundException("One or more permissions not found. Provided: " + String.join(", ", createRequestDto.permissionNames()) + ". Found: " + foundPermissionNames);
            }
        }

        Role role = new Role();
        role.setName(roleType);
        role.setPermissions(assignedPermissions);

        Role savedRole = roleRepository.save(role);
        log.info("Role created successfully with ID: {} and name: {}", savedRole.getId(), savedRole.getName());
        return roleMapper.toRoleResponseDto(savedRole);
    }

    @Override
    @Transactional(readOnly = true)
    public List<RoleDto.RoleResponseDto> getAllRoles() {
        log.info("Fetching all roles");
        return roleRepository.findAll().stream()
                .map(roleMapper::toRoleResponseDto)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(readOnly = true)
    public RoleDto.RoleResponseDto getRoleById(Long roleId) {
        log.info("Fetching role by ID: {}", roleId);
        Role role = roleRepository.findById(roleId)
                .orElseThrow(() -> {
                    log.warn("Role not found with ID: {}", roleId);
                    return new ResourceNotFoundException("Role not found with ID: " + roleId);
                });
        return roleMapper.toRoleResponseDto(role);
    }

    @Override
    @Transactional(readOnly = true)
    public RoleDto.RoleResponseDto getRoleByName(String roleName) {
        log.info("Fetching role by name: {}", roleName);
        Role.RoleType roleType = convertRoleNameToEnum(roleName);
        Role role = roleRepository.findByName(roleType)
                .orElseThrow(() -> {
                    log.warn("Role not found with name: {}", roleName);
                    return new ResourceNotFoundException("Role not found with name: " + roleName);
                });
        return roleMapper.toRoleResponseDto(role);
    }

    @Override
    @Transactional
    public RoleDto.RoleResponseDto updateRole(Long roleId, RoleDto.RoleUpdateRequestDto updateRequestDto) {
        log.info("Attempting to update role with ID: {}", roleId);
        Role roleToUpdate = roleRepository.findById(roleId)
                .orElseThrow(() -> new ResourceNotFoundException("Role not found with ID: " + roleId));

        // Name update (if provided and different)
        if (StringUtils.hasText(updateRequestDto.name())) {
            Role.RoleType newRoleType = convertRoleNameToEnum(updateRequestDto.name());
            if (newRoleType != roleToUpdate.getName()) {
                // Check if new name already exists for another role
                roleRepository.findByName(newRoleType).ifPresent(existingRole -> {
                    if (!existingRole.getId().equals(roleId)) {
                        log.warn("Role name '{}' already exists for another role.", newRoleType.name());
                        throw new ResourceAlreadyExistsException("Role name '" + newRoleType.name() + "' already exists.");
                    }
                });
                roleToUpdate.setName(newRoleType);
                log.info("Updating role ID: {} to new name: {}", roleId, newRoleType.name());
            }
        }

        // Permissions update (if provided)
        if (updateRequestDto.permissionNames() != null) {
            Set<Permission> newPermissions = new HashSet<>();
            if (!updateRequestDto.permissionNames().isEmpty()) {
                newPermissions = permissionRepository.findByPermissionNameIn(updateRequestDto.permissionNames());
                if (newPermissions.size() != updateRequestDto.permissionNames().size()) {
                    log.warn("One or more permissions not found for role update: {}", updateRequestDto.permissionNames());
                    String foundPermissionNames = newPermissions.stream().map(Permission::getPermissionName).collect(Collectors.joining(", "));
                    throw new ResourceNotFoundException("One or more permissions not found for update. Provided: " + String.join(", ", updateRequestDto.permissionNames()) + ". Found: " + foundPermissionNames);
                }
            }
            roleToUpdate.setPermissions(newPermissions); // Replaces all existing permissions
            log.info("Updating permissions for role ID: {}. New permissions count: {}", roleId, newPermissions.size());
        }

        Role updatedRole = roleRepository.save(roleToUpdate);
        log.info("Role updated successfully for ID: {}", updatedRole.getId());
        return roleMapper.toRoleResponseDto(updatedRole);
    }

    @Override
    @Transactional
    public void deleteRole(Long roleId) {
        log.info("Attempting to delete role with ID: {}", roleId);
        Role roleToDelete = roleRepository.findById(roleId)
                .orElseThrow(() -> new ResourceNotFoundException("Role not found with ID: " + roleId));

        if (roleToDelete.getName() == Role.RoleType.ROLE_ADMIN || roleToDelete.getName() == Role.RoleType.ROLE_USER) {
            log.warn("Attempted to delete a core system role: {}", roleToDelete.getName());
            throw new BadRequestException("Cannot delete core system role: " + roleToDelete.getName());
        }

        roleRepository.delete(roleToDelete);
        log.info("Role deleted successfully with ID: {}", roleId);
    }

    @Override
    @Transactional(readOnly = true)
    public List<PermissionDto> getAllPermissions() {
        log.info("Fetching all available permissions");
        return permissionRepository.findAll().stream()
                .map(permissionMapper::toPermissionDto)
                .collect(Collectors.toList());
    }

    private Role.RoleType convertRoleNameToEnum(String roleName) {
        try {
            return Role.RoleType.valueOf(roleName.trim().toUpperCase());
        } catch (IllegalArgumentException e) {
            log.warn("Invalid role name string: '{}' during conversion to enum.", roleName, e);
            throw new BadRequestException("Invalid role name provided: " + roleName + ". Ensure it matches defined roles (e.g., ROLE_USER, ROLE_ADMIN).");
        }
    }
}
