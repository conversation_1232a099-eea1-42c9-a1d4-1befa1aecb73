package io.skillify.services.job;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import io.skillify.dtos.job.JobDto;
import io.skillify.exceptions.ResourceNotFoundException;
import io.skillify.mappers.JobMapper;
import io.skillify.models.job.Job;
import io.skillify.models.org.Organization;
import io.skillify.repositories.job.JobRepository;
import io.skillify.repositories.org.OrganizationRepository;
import lombok.RequiredArgsConstructor;

@Service
@RequiredArgsConstructor
@Transactional(readOnly = true)
public class JobService {
    private final JobRepository jobRepository;
    private final OrganizationRepository organizationRepository;
    private final JobMapper jobMapper;

    @Transactional
    public JobDto.Response createJob(UUID organizationId, JobDto.CreateRequest request, Authentication authentication) {
        Organization organization = organizationRepository.findById(organizationId)
                .orElseThrow(() -> new ResourceNotFoundException("Organization not found"));

        Job job = Job.builder()
                .title(request.getTitle())
                .description(request.getDescription())
                .organization(organization)
                .isActive(true)
                .build();

        return jobMapper.toResponse(jobRepository.save(job));
    }

    @Transactional
    public JobDto.Response updateJob(UUID jobId, JobDto.UpdateRequest request, Authentication authentication) {
        Job job = jobRepository.findById(jobId)
                .orElseThrow(() -> new ResourceNotFoundException("Job not found"));

        updateJobFromRequest(job, request);
        return jobMapper.toResponse(job);
    }

    public JobDto.Response getJob(UUID jobId) {
        Job job = jobRepository.findById(jobId)
                .orElseThrow(() -> new ResourceNotFoundException("Job not found"));

        return jobMapper.toResponse(job);
    }

    public List<JobDto.Response> getOrganizationJobs(UUID organizationId) {
        return jobRepository.findByOrganizationId(organizationId).stream()
                .map(jobMapper::toResponse)
                .toList();
    }

    @Transactional
    public void deleteJob(UUID jobId) {
        if (!jobRepository.existsById(jobId)) {
            throw new ResourceNotFoundException("Job not found");
        }
        jobRepository.deleteById(jobId);
    }

    private void updateJobFromRequest(Job job, JobDto.UpdateRequest request) {
        Optional.ofNullable(request.getTitle()).ifPresent(job::setTitle);
        Optional.ofNullable(request.getDescription()).ifPresent(job::setDescription);
        Optional.ofNullable(request.getIsActive()).ifPresent(job::setActive);
    }
}
