package io.skillify.services.auth;

import java.time.OffsetDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;

import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import io.skillify.config.security.CookieUtil;
import io.skillify.config.security.JwtUtil;
import io.skillify.dtos.ResponseDto;
import io.skillify.dtos.auth.CheckCredentialsRequest;
import io.skillify.dtos.auth.ContinueWithOAuth;
import io.skillify.dtos.user.UserDto;
import io.skillify.dtos.user.UserDto.UserResponseDto;
import io.skillify.mappers.UserMapper;
import io.skillify.models.RefreshToken;
import io.skillify.models.user.Role;
import io.skillify.models.user.User;
import io.skillify.repositories.auth.RefreshTokenRepository;
import io.skillify.repositories.user.RoleRepository;
import io.skillify.repositories.user.UserRepository;
import io.skillify.services.subscription.SubscriptionService;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Service
@RequiredArgsConstructor
@Transactional
@Slf4j
public class AuthService {

    private final AuthenticationManager authenticationManager;
    private final UserRepository userRepository;
    private final RoleRepository roleRepository;
    private final RefreshTokenRepository refreshTokenRepository;
    private final JwtUtil jwtUtil;
    private final PasswordEncoder passwordEncoder;
    private final CookieUtil cookieUtil;
    private final HttpServletResponse response;
    private final UserMapper userMapper;
    private final SubscriptionService subscriptionService;

    private static final long REFRESH_TOKEN_DURATION_DAYS = 7;

    @Transactional
    public ResponseDto<?> login(String email, String password) {
        authenticationManager
                .authenticate(new UsernamePasswordAuthenticationToken(email, password));

        User user = userRepository.findByEmail(email)
                .or(() -> userRepository.findByUsername(email))
                .orElseThrow(() -> new RuntimeException("User not found"));

        OffsetDateTime now = OffsetDateTime.now();
        user.setLastLogin(now);
        user = userRepository.save(user);

        // Create access token
        String accessToken = jwtUtil.generateToken(user);
        cookieUtil.createJwtCookie(accessToken, response);

        // Create refresh token
        RefreshToken refreshToken = createRefreshToken(user);
        cookieUtil.createRefreshTokenCookie(refreshToken.getToken(), response);

        Map<String, Object> data = new HashMap<>();
        data.put("refreshToken", refreshToken.getToken());
        data.put("accessToken", accessToken);
        data.put("user", userMapper.toUserResponseDto(user));
        return ResponseDto.success(data);
    }

    @Transactional
    public UserResponseDto register(User user, String roleName) {
        if (userRepository.findByUsername(user.getUsername()).isPresent() ||
                userRepository.findByEmail(user.getEmail()).isPresent()) {
            throw new RuntimeException("Username or email already exists");
        }

        user.setPassword(passwordEncoder.encode(user.getPassword()));

        Optional<Role> role = roleRepository.findByName(Role.RoleType.valueOf(roleName));
        if (role.isEmpty()) {
            throw new RuntimeException("Role not found");
        }
        user.setRoles(Set.of(role.get()));

        OffsetDateTime now = OffsetDateTime.now();
        user.setCreatedAt(now);
        user.setUpdatedAt(now);
        user.setLastLogin(now);

        user.setVerified(true);

        User savedUser = userRepository.save(user);

        try {
            subscriptionService.createDefaultFreeSubscription(savedUser.getId());
        } catch (Exception e) {
            log.error("Failed to create default subscription for user {}", savedUser.getId(), e);
            throw new RuntimeException("Failed to create default subscription for user");
        }

        UserDto.UserResponseDto userDto = userMapper.toUserResponseDto(savedUser);
        return userDto;
    }

    @Transactional
    public ResponseDto<?> oauth(ContinueWithOAuth request) {
        String email = request.getEmail();
        String oauthId = request.getOauthId();
        String name = request.getName();
        String roleName = request.getRole();

        Optional<User> existingUserByEmail = userRepository.findByEmail(email);
        Optional<User> existingUserByOauthId = userRepository.findByOauthId(oauthId);

        // Case 1: OauthId already exists but doesn't match email — conflict
        if (existingUserByOauthId.isPresent() &&
            !existingUserByOauthId.get().getEmail().equals(email)) {
            throw new RuntimeException("OAuth ID already in use with a different email");
        }

        User user;

        if (existingUserByEmail.isPresent()) {
            user = existingUserByEmail.get();

            // Case 2: Email exists but oauthId is different (possible normal account)
            if (user.getOauthId() == null) {
                // Optional: enforce user verification if you want to prevent accidental hijacking
                user.setOauthId(oauthId);
            } else if (!user.getOauthId().equals(oauthId)) {
                throw new RuntimeException("Email is already registered with another login method");
            }
        } else {
            // Case 3: Fully new user
            user = User.builder()
                    .email(email)
                    .username(generateUniqueUsername(name))
                    .oauthId(oauthId)
                    .password(passwordEncoder.encode(UUID.randomUUID().toString()))
                    .verified(true)
                    .build();

            Role.RoleType roleType = Role.RoleType.valueOf(roleName);
            Role role = roleRepository.findByName(roleType)
                    .orElseThrow(() -> new RuntimeException("Role not found"));

            user.setRoles(Set.of(role));
        }

        OffsetDateTime now = OffsetDateTime.now();
        user.setLastLogin(now);
        user = userRepository.save(user);

        if (existingUserByEmail.isEmpty()) {
            try {
                subscriptionService.createDefaultFreeSubscription(user.getId());
            } catch (Exception e) {
                log.error("Failed to create default subscription for OAuth user {}", user.getId(), e);
                throw new RuntimeException("Failed to create default subscription for OAuth user");
            }
        }

        String accessToken = jwtUtil.generateToken(user);
        cookieUtil.createJwtCookie(accessToken, response);

        RefreshToken refreshToken = createRefreshToken(user);
        cookieUtil.createRefreshTokenCookie(refreshToken.getToken(), response);

        Map<String, Object> data = new HashMap<>();
        data.put("refreshToken", refreshToken.getToken());
        data.put("accessToken", accessToken);
        data.put("user", userMapper.toUserResponseDto(user));
        return ResponseDto.success(data);
    }

    @Transactional
    public String refreshToken(HttpServletRequest request) {
        String refreshTokenStr = cookieUtil.getRefreshTokenFromCookies(request);
        if (refreshTokenStr == null) {
            throw new RuntimeException("Refresh token missing");
        }
        RefreshToken storedToken = refreshTokenRepository.findByToken(refreshTokenStr)
                .orElseThrow(() -> new RuntimeException("Invalid refresh token"));
        if (storedToken.getExpiryDate().isBefore(OffsetDateTime.now())) {
            refreshTokenRepository.delete(storedToken);
            throw new RuntimeException("Refresh token expired");
        }
        User user = storedToken.getUser();

        // Remove only the current refresh token
        refreshTokenRepository.delete(storedToken);

        // Create a new refresh token and new JWT
        RefreshToken newRefreshToken = createRefreshToken(user);
        cookieUtil.createRefreshTokenCookie(newRefreshToken.getToken(), response);
        String newAccessToken = jwtUtil.generateToken(user);
        cookieUtil.createJwtCookie(newAccessToken, response);
        return "Token refreshed successfully";
    }

    public void logout(HttpServletRequest request) {
        String refreshTokenStr = cookieUtil.getRefreshTokenFromCookies(request);
        if (refreshTokenStr != null) {
            refreshTokenRepository.findByToken(refreshTokenStr)
                    .ifPresent(token -> refreshTokenRepository.delete(token));
        }
        cookieUtil.deleteJwtCookie(response, CookieUtil.JWT_COOKIE_NAME);
        cookieUtil.deleteRefreshTokenCookie(response);
    }

    private RefreshToken createRefreshToken(User user) {
        RefreshToken token = new RefreshToken();
        token.setToken(UUID.randomUUID().toString() + UUID.randomUUID().toString());
        token.setUser(user);
        token.setExpiryDate(OffsetDateTime.now().plusDays(REFRESH_TOKEN_DURATION_DAYS));
        return refreshTokenRepository.save(token);
    }

    public ResponseDto<?> checkCredentials(CheckCredentialsRequest request) {
        String email = request.getEmail();
        String username = request.getUsername();

        Map<String, Boolean> data = new HashMap<>();

        data.put("emailValid", !userRepository.findByEmail(email).isPresent());
        data.put("usernameValid", !userRepository.findByUsername(username).isPresent());

        return ResponseDto.success(data);
    }

    private String generateUniqueUsername(String name) {
        String baseUsername = name.replaceAll("[^a-zA-Z0-9]", "").toLowerCase();
        String username = baseUsername;
        int suffix = 1;
        while (userRepository.findByUsername(username).isPresent()) {
            username = baseUsername + suffix;
            suffix++;
        }
        return username;
    }
}
