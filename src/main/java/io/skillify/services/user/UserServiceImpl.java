package io.skillify.services.user;

import java.util.HashSet;
import java.util.Objects;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import io.skillify.dtos.user.UserDto;
import io.skillify.exceptions.BadRequestException;
import io.skillify.exceptions.ResourceAlreadyExistsException;
import io.skillify.exceptions.ResourceNotFoundException;
import io.skillify.mappers.UserMapper;
import io.skillify.models.user.Role;
import io.skillify.models.user.User;
import io.skillify.repositories.user.RoleRepository;
import io.skillify.repositories.user.UserRepository;
import io.skillify.services.subscription.SubscriptionService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Service
@RequiredArgsConstructor
@Slf4j
public class UserServiceImpl implements UserService {

    private final UserRepository userRepository;
    private final RoleRepository roleRepository;
    private final UserMapper userMapper;
    private final PasswordEncoder passwordEncoder;
    private final SubscriptionService subscriptionService;

    @Override
    @Transactional
    public UserDto.UserResponseDto adminCreateUser(UserDto.UserCreateRequestDto createRequestDto) {
        log.info("Admin creating user with username: {} and email: {}", createRequestDto.username(),
                createRequestDto.email());
        if (userRepository.existsByUsername(createRequestDto.username())) {
            throw new ResourceAlreadyExistsException("Username '" + createRequestDto.username() + "' already exists.");
        }
        if (userRepository.existsByEmail(createRequestDto.email())) {
            throw new ResourceAlreadyExistsException("Email '" + createRequestDto.email() + "' already exists.");
        }

        // Create new user entity
        User user = userMapper.toUser(createRequestDto);
        user.setPassword(passwordEncoder.encode(createRequestDto.password()));
        user.setVerified(createRequestDto.verified() != null ? createRequestDto.verified() : false);

        // Set roles
        Set<Role> roles = new HashSet<>();
        if (createRequestDto.roleNames() != null && !createRequestDto.roleNames().isEmpty()) {
            roles = mapRoleNamesToRoles(createRequestDto.roleNames());
        } else {
            Role userRole = roleRepository.findByName(Role.RoleType.ROLE_USER)
                    .orElseThrow(() -> {
                        log.error("Default role ROLE_USER not found in the database.");
                        return new ResourceNotFoundException("Default role ROLE_USER not found. Cannot create user.");
                    });
            roles.add(userRole);
        }
        user.setRoles(roles);

        // Save user
        User savedUser = userRepository.save(user);
        log.info("User created by admin with ID: {}", savedUser.getId());

        subscriptionService.createDefaultFreeSubscription(savedUser.getId());

        return userMapper.toUserResponseDto(savedUser);
    }

    @Override
    @Transactional(readOnly = true)
    public UserDto.UserResponseDto getCurrentUserDetails() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication == null || !authentication.isAuthenticated()
                || "anonymousUser".equals(authentication.getPrincipal())) {
            log.warn("Attempt to get current user details for unauthenticated user.");
            throw new UsernameNotFoundException("No authenticated user found or user is anonymous.");
        }
        String authenticatedUserEmail = authentication.getName();
        log.debug("Fetching current user details for principal (email): {}", authenticatedUserEmail);
        User user = userRepository.findByEmail(authenticatedUserEmail)
                .orElseThrow(() -> {
                    log.warn("Authenticated user (email: {}) not found in database.", authenticatedUserEmail);
                    return new UsernameNotFoundException(
                            "Authenticated user not found in database: " + authenticatedUserEmail);
                });
        return userMapper.toUserResponseDto(user);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<UserDto.UserResponseDto> getAllUsers(Pageable pageable) {
        log.info("Fetching all users with page request: {}", pageable);
        return userRepository.findAll(pageable).map(userMapper::toUserResponseDto);
    }

    @Override
    @Transactional(readOnly = true)
    public UserDto.UserResponseDto getUserById(UUID userId) {
        log.info("Fetching user by ID: {}", userId);
        return userRepository.findById(userId)
                .map(userMapper::toUserResponseDto)
                .orElseThrow(() -> new ResourceNotFoundException("User not found with ID: " + userId));
    }

    @Override
    @Transactional(readOnly = true)
    public UserDto.UserResponseDto getUserByUsername(String username) {
        log.info("Fetching user by actual username: {}", username);
        return userRepository.findByUsername(username)
                .map(userMapper::toUserResponseDto)
                .orElseThrow(() -> new ResourceNotFoundException("User not found with username: " + username));
    }

    @Override
    @Transactional(readOnly = true)
    public UserDto.UserResponseDto getUserByEmail(String email) {
        log.info("Fetching user by email: {}", email);
        return userRepository.findByEmail(email)
                .map(userMapper::toUserResponseDto)
                .orElseThrow(() -> new ResourceNotFoundException("User not found with email: " + email));
    }

    @Override
    @Transactional
    public UserDto.UserResponseDto updateUserProfile(UUID userId, UserDto.UserProfileUpdateRequestDto dto) {
        log.info("Attempting to update profile for user ID: {}", userId);
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new ResourceNotFoundException("User not found with ID: " + userId));

        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        String currentPrincipalEmail = authentication != null ? authentication.getName() : null;

        if (!Objects.equals(currentPrincipalEmail, user.getEmail())) {
            throw new AccessDeniedException("You are not authorized to update this user's profile.");
        }

        boolean modified = false;

        if (StringUtils.hasText(dto.username()) && !dto.username().equalsIgnoreCase(user.getName())) {
            validateUsernameIsUnique(dto.username(), user.getId());
            modified = true;
        }

        if (StringUtils.hasText(dto.email()) && !dto.email().equalsIgnoreCase(user.getEmail())) {
            validateEmailIsUnique(dto.email(), user.getId());
            modified = true;
        }

        if (modified) {
            userMapper.updateUserProfileFromDto(dto, user); // Assuming this DTO only has username/email
            userRepository.save(user);
            log.info("User profile updated for ID: {}", user.getId());
        } else {
            log.info("User profile not changed (no valid updates in DTO or values are the same) for ID: {}",
                    user.getId());
        }
        return userMapper.toUserResponseDto(user);
    }

    @Override
    @Transactional
    public UserDto.UserResponseDto adminUpdateUser(UUID userId, UserDto.AdminUserUpdateRequestDto dto) {
        log.info("Admin attempting to update user ID: {}", userId);
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new ResourceNotFoundException("User not found with ID: " + userId));

        boolean modified = false;

        if (StringUtils.hasText(dto.username()) && !dto.username().equalsIgnoreCase(user.getName())) {
            validateUsernameIsUnique(dto.username(), user.getId());
            modified = true;
        }

        if (StringUtils.hasText(dto.email()) && !dto.email().equalsIgnoreCase(user.getEmail())) {
            validateEmailIsUnique(dto.email(), user.getId());
            modified = true;
        }

        if (dto.verified() != null && user.isVerified() != dto.verified()) {
            modified = true;
        }

        if (dto.roleNames() != null) {
            Set<Role> newRoles = mapRoleNamesToRoles(dto.roleNames());
            if (!user.getRoles().equals(newRoles)) {
                user.setRoles(newRoles);
                modified = true;
            }
        }

        if (modified) {
            userMapper.adminUpdateUserFromDto(dto, user);
            userRepository.save(user);
            log.info("User updated by admin, ID: {}", user.getId());
        } else {
            log.info("User not changed by admin (no valid updates in DTO or values are the same), ID: {}",
                    user.getId());
        }
        return userMapper.toUserResponseDto(user);
    }

    @Override
    @Transactional
    public void adminDeleteUser(UUID userId) {
        log.info("Admin deleting user ID: {}", userId);
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new ResourceNotFoundException("User not found with ID: " + userId));
        userRepository.delete(user);
        log.info("User deleted by admin, ID: {}", userId);
    }

    @Override
    @Transactional
    public void changeUserPassword(UUID userId, UserDto.PasswordChangeRequestDto requestDto) {
        log.info("Attempting to change password for user ID: {}", userId);
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new ResourceNotFoundException("User not found with ID: " + userId));

        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        String currentPrincipalEmail = authentication != null ? authentication.getName() : null;

        if (!Objects.equals(currentPrincipalEmail, user.getEmail())) {
            throw new AccessDeniedException("You are not authorized to change this user's password.");
        }

        if (!passwordEncoder.matches(requestDto.oldPassword(), user.getPassword())) {
            log.warn("Old password does not match for user ID: {}", userId);
            throw new BadRequestException("Incorrect old password.");
        }
        if (requestDto.oldPassword().equals(requestDto.newPassword())) {
            throw new BadRequestException("New password cannot be the same as the old password.");
        }
        user.setPassword(passwordEncoder.encode(requestDto.newPassword()));
        userRepository.save(user);
        log.info("Password changed successfully for user ID: {}", userId);
    }

    @Override
    @Transactional
    public void adminResetUserPassword(UUID userId, UserDto.AdminPasswordResetRequestDto requestDto) {
        log.info("Admin resetting password for user ID: {}", userId);
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new ResourceNotFoundException("User not found with ID: " + userId));
        user.setPassword(passwordEncoder.encode(requestDto.newPassword()));
        userRepository.save(user);
        log.info("Password reset by admin for user ID: {}", userId);
    }

    private Set<Role> mapRoleNamesToRoles(Set<String> roleNames) {
        if (roleNames == null || roleNames.isEmpty()) {
            return new HashSet<>();
        }
        return roleNames.stream()
                .map(this::convertRoleNameToEnumSafe)
                .filter(Objects::nonNull)
                .map(roleType -> roleRepository.findByName(roleType)
                        .orElseThrow(() -> new ResourceNotFoundException("Role not found: " + roleType.name())))
                .collect(Collectors.toSet());
    }

    private Role.RoleType convertRoleNameToEnumSafe(String roleName) {
        if (!StringUtils.hasText(roleName)) {
            throw new BadRequestException("Role name cannot be null or empty.");
        }
        try {
            return Role.RoleType.valueOf(roleName.trim().toUpperCase());
        } catch (IllegalArgumentException e) {
            log.warn("Invalid role name string: '{}' during conversion to enum.", roleName, e);
            throw new BadRequestException("Invalid role name provided: " + roleName
                    + ". Ensure it matches defined roles (e.g., ROLE_USER, ROLE_ADMIN).");
        }
    }

    private void validateUsernameIsUnique(String username, UUID currentUserIdToExclude) {
        userRepository.findByUsername(username).ifPresent(existingUser -> {
            if (!existingUser.getId().equals(currentUserIdToExclude)) {
                log.warn("Validation failed: Username '{}' is already taken by user ID '{}'", username,
                        existingUser.getId());
                throw new ResourceAlreadyExistsException("Username '" + username + "' is already taken.");
            }
        });
    }

    private void validateEmailIsUnique(String email, UUID currentUserIdToExclude) {
        userRepository.findByEmail(email).ifPresent(existingUser -> {
            if (!existingUser.getId().equals(currentUserIdToExclude)) {
                log.warn("Validation failed: Email '{}' is already taken by user ID '{}'", email, existingUser.getId());
                throw new ResourceAlreadyExistsException("Email '" + email + "' is already taken.");
            }
        });
    }
}
