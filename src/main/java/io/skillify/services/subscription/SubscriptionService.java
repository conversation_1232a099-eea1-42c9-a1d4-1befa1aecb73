package io.skillify.services.subscription;

import java.time.OffsetDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.dao.DataAccessException;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.dao.IncorrectResultSizeDataAccessException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import io.skillify.constants.SubscriptionConstants;
import io.skillify.dtos.subscription.SubscriptionDto;
import io.skillify.exceptions.BadRequestException;
import io.skillify.exceptions.ResourceNotFoundException;
import io.skillify.mappers.SubscriptionMapper;
import io.skillify.models.subscription.SubscriptionPlan;
import io.skillify.models.subscription.SubscriptionStatus;
import io.skillify.models.subscription.UserSubscription;
import io.skillify.repositories.org.OrganizationRepository;
import io.skillify.repositories.subscription.SubscriptionPlanRepository;
import io.skillify.repositories.subscription.UserSubscriptionRepository;
import io.skillify.repositories.user.UserRepository;
import jakarta.persistence.NonUniqueResultException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
@RequiredArgsConstructor
@Transactional
public class SubscriptionService {

    private final UserSubscriptionRepository subscriptionRepository;
    private final SubscriptionPlanRepository planRepository;
    private final OrganizationRepository organizationRepository;
    private final UserRepository userRepository;
    private final SubscriptionMapper subscriptionMapper;

    public SubscriptionStatus getUserSubscriptionStatus(UUID userId) {
        var subscription = getUserActiveSubscription(userId);
        var currentOrgCount = getCurrentOrganizationCount(userId);
        return new SubscriptionStatus(subscription, currentOrgCount);
    }

    public boolean hasReachedOrganizationLimit(UUID userId) {
        var status = getUserSubscriptionStatus(userId);
        if (status.hasReachedLimit()) {
            log.info("User {} reached org limit: {}/{} on {}", userId,
                    status.getCurrentOrganizationCount(), status.getMaxOrganizations(), status.getPlanName());
        }
        return status.hasReachedLimit();
    }

    @Cacheable(value = "user-subscriptions", key = "#userId")
    public UserSubscription getUserActiveSubscription(UUID userId) {
        return findActiveSubscription(userId)
                .orElseGet(() -> {
                    try {
                        return createDefaultFreeSubscription(userId);
                    } catch (DataIntegrityViolationException e) {
                        return findActiveSubscription(userId)
                                .orElseThrow(() -> new IllegalStateException(
                                        "Unable to retrieve subscription after creation"));
                    }
                });
    }

    public long getCurrentOrganizationCount(UUID userId) {
        return organizationRepository.countByCreatedById(userId);
    }

    public int getMaxOrganizations(UUID userId) {
        return getUserActiveSubscription(userId).getPlan().getMaxOrganizations();
    }

    public List<UserSubscription> getUserSubscriptionHistory(UUID userId) {
        return subscriptionRepository.findAllByUserIdOrderByCreatedAtDesc(userId);
    }

    @CacheEvict(value = "user-subscriptions", key = "#userId")
    public UserSubscription updateUserSubscription(UUID userId, String planName) {
        var subscription = getExistingActiveSubscription(userId);
        var newPlan = validateAndGetPlan(planName);
        var oldPlan = subscription.getPlan();

        subscription.setPlan(newPlan);

        // Set billing period if switching to a paid plan
        if (newPlan.requiresPayment() && !oldPlan.requiresPayment()) {
            setBillingPeriod(subscription, OffsetDateTime.now());
        }
        // Clear billing period if switching to free plan
        else if (!newPlan.requiresPayment() && oldPlan.requiresPayment()) {
            subscription.setCurrentPeriodStart(null);
            subscription.setCurrentPeriodEnd(null);
        }

        var updated = saveSubscription(subscription);

        log.info("Updated subscription for user {} from {} to {}", userId, oldPlan.getName(), planName);
        return updated;
    }

    @CacheEvict(value = "user-subscriptions", key = "#userId")
    public UserSubscription cancelUserSubscription(UUID userId, boolean immediate) {
        var subscription = getExistingActiveSubscription(userId);

        subscription.setStatus(SubscriptionConstants.SubscriptionStatus.CANCELLED);
        if (immediate) {
            subscription.markAsEnded();
        }

        var cancelled = saveSubscription(subscription);
        log.info("Cancelled subscription for user {} (immediate: {})", userId, immediate);
        return cancelled;
    }

    @CacheEvict(value = "user-subscriptions", key = "#userId")
    public UserSubscription suspendUserSubscription(UUID userId, String reason) {
        var subscription = getExistingActiveSubscription(userId);
        subscription.setStatus(SubscriptionConstants.SubscriptionStatus.SUSPENDED);

        var suspended = saveSubscription(subscription);
        log.info("Suspended subscription for user {} - reason: {}", userId, reason);
        return suspended;
    }

    @CacheEvict(value = "user-subscriptions", key = "#userId")
    public UserSubscription createTrialSubscription(UUID userId, String planName, int trialDays) {
        validateTrialEligibility(userId, planName);
        var plan = validateAndGetPlan(planName);

        try {
            var now = OffsetDateTime.now();
            var subscription = createSubscription(userId, plan, SubscriptionConstants.SubscriptionStatus.TRIAL);
            var trialEnd = now.plusDays(trialDays);

            subscription.setTrialEnd(trialEnd);
            subscription.setEndedAt(trialEnd);

            // For paid plans, set billing period to start after trial ends
            if (plan.requiresPayment()) {
                subscription.setCurrentPeriodStart(trialEnd);
                subscription.setCurrentPeriodEnd(trialEnd.plusMonths(1));
            }

            var saved = saveSubscription(subscription);

            log.info("Created trial subscription for user {} - plan: {}, days: {}, trial ends: {}",
                    userId, planName, trialDays, trialEnd);
            return saved;
        } catch (DataIntegrityViolationException e) {
            throw createSubscriptionConstraintException(e,
                    "Unable to start trial. You already have an active subscription. " +
                            "Please cancel your current subscription first to start a trial.");
        }
    }

    @CacheEvict(value = "user-subscriptions", key = "#userId")
    public UserSubscription createDefaultFreeSubscription(UUID userId) {
        try {
            validateNoActiveSubscription(userId);

            var freePlan = getFreePlan();
            var subscription = createActiveSubscription(userId, freePlan);

            log.info("Created default free subscription for user {}", userId);
            return subscription;

        } catch (DataIntegrityViolationException e) {
            throw createSubscriptionConstraintException(e,
                    "Unable to create free subscription. User already has an active subscription.");
        }
    }

    public void expireSubscriptions() {
        var now = OffsetDateTime.now();
        var expiredSubscriptions = subscriptionRepository.findActiveSubscriptionsPassedEndDate(now);

        expiredSubscriptions.forEach(subscription -> {
            subscription.setStatus(SubscriptionConstants.SubscriptionStatus.EXPIRED);
            log.info("Expired subscription {} for user {}",
                    subscription.getId(), subscription.getUser().getId());
        });

        subscriptionRepository.saveAll(expiredSubscriptions);
        log.info("Processed {} expired subscriptions", expiredSubscriptions.size());
    }

    public List<SubscriptionPlan> getAvailablePlansForUser(UUID userId) {
        var allPlans = planRepository.findByIsActiveTrue();
        var userHistory = getUserSubscriptionHistory(userId);
        var currentSubscription = getUserActiveSubscription(userId);

        return allPlans.stream()
                .filter(plan -> isPlanAvailableForUser(plan, userHistory, currentSubscription))
                .toList();
    }

    public List<SubscriptionDto.AvailablePlanResponse> getDetailedAvailablePlansForUser(UUID userId) {
        var allPlans = planRepository.findByIsActiveTrue();
        var currentSubscription = getUserActiveSubscription(userId);

        return allPlans.stream()
                .map(plan -> createAvailablePlanResponse(plan, userId, currentSubscription))
                .toList();
    }

    public boolean isPlanAvailableForUser(UUID userId, String planName) {
        return planRepository.findByName(planName)
                .map(plan -> {
                    var userHistory = getUserSubscriptionHistory(userId);
                    var currentSubscription = getUserActiveSubscription(userId);
                    return isPlanAvailableForUser(plan, userHistory, currentSubscription);
                })
                .orElse(false);
    }

    public boolean canUserStartTrialForPlan(UUID userId, String planName) {
        return !hasUserUsedTrialForPlan(userId, planName)
                && !hasActiveTrialSubscription(userId)
                && !SubscriptionConstants.PlanNames.FREE.equals(planName)
                && !isCurrentUserPlan(userId, planName);
    }

    private UserSubscription createActiveSubscription(UUID userId, SubscriptionPlan plan) {
        return createSubscription(userId, plan, SubscriptionConstants.SubscriptionStatus.ACTIVE);
    }

    private UserSubscription createSubscription(UUID userId, SubscriptionPlan plan, String status) {
        var user = userRepository.getReferenceById(userId);
        var now = OffsetDateTime.now();

        var subscription = UserSubscription.builder()
                .user(user)
                .plan(plan)
                .status(status)
                .startedAt(now)
                .build();

        // Set billing period for paid plans
        if (plan.requiresPayment()) {
            setBillingPeriod(subscription, now);
        }

        return saveSubscription(subscription);
    }

    private UserSubscription saveSubscription(UserSubscription subscription) {
        return subscriptionRepository.save(subscription);
    }

    private void setBillingPeriod(UserSubscription subscription, OffsetDateTime startTime) {
        // For paid plans, set a monthly billing cycle by default
        subscription.setCurrentPeriodStart(startTime);
        subscription.setCurrentPeriodEnd(startTime.plusMonths(1));

        log.debug("Set billing period for subscription {} from {} to {}",
                subscription.getId(), startTime, startTime.plusMonths(1));
    }

    private void endSubscription(UserSubscription subscription) {
        subscription.setStatus(SubscriptionConstants.SubscriptionStatus.CANCELLED);
        subscription.markAsEnded();
        subscriptionRepository.saveAndFlush(subscription);
    }

    private void validateTrialEligibility(UUID userId, String planName) {
        if (hasUserUsedTrialForPlan(userId, planName)) {
            throw new BadRequestException(SubscriptionConstants.ErrorMessages.TRIAL_ALREADY_USED);
        }

        if (hasActiveTrialSubscription(userId)) {
            throw new BadRequestException(SubscriptionConstants.ErrorMessages.ACTIVE_TRIAL_EXISTS);
        }

        if (isCurrentUserPlan(userId, planName)) {
            throw new BadRequestException(
                    "You cannot start a trial for the plan you're currently using. " +
                            "Please choose a different plan.");
        }
    }

    private void validateNoActiveSubscription(UUID userId) {
        if (findActiveSubscription(userId).isPresent()) {
            throw new BadRequestException("User already has an active subscription");
        }
    }

    private SubscriptionPlan validateAndGetPlan(String planName) {
        return planRepository.findByName(planName)
                .orElseThrow(() -> new ResourceNotFoundException("Subscription plan not found: " + planName));
    }

    private Optional<UserSubscription> findActiveSubscription(UUID userId) {
        try {
            return subscriptionRepository.findByUserIdAndStatus(userId,
                    SubscriptionConstants.SubscriptionStatus.ACTIVE);
        } catch (NonUniqueResultException e) {
            log.error("Multiple active subscriptions found for user {}: {}", userId, e.getMessage());
            throw new BadRequestException(
                    "Multiple active subscriptions found for user. Please contact support to resolve this issue.");
        } catch (IncorrectResultSizeDataAccessException e) {
            log.error("Incorrect result size when finding active subscription for user {}: expected 0 or 1, got {}",
                    userId, e.getActualSize());
            throw new BadRequestException(
                    "Multiple active subscriptions found for user. Please contact support to resolve this issue.");
        } catch (DataAccessException e) {
            log.error("Database error while finding active subscription for user {}: {}", userId, e.getMessage(), e);
            throw new BadRequestException(
                    "Unable to retrieve subscription information. Please try again or contact support.");
        }
    }

    private UserSubscription getExistingActiveSubscription(UUID userId) {
        return findActiveSubscription(userId)
                .orElseThrow(() -> new ResourceNotFoundException("No active subscription found for user"));
    }

    private SubscriptionPlan getFreePlan() {
        return validateAndGetPlan(SubscriptionConstants.PlanNames.FREE);
    }

    private boolean isCurrentUserPlan(UUID userId, String planName) {
        var currentSubscription = getUserActiveSubscription(userId);
        return currentSubscription.getPlan().getName().equals(planName);
    }

    private boolean hasUserUsedTrialForPlan(UUID userId, String planName) {
        return subscriptionRepository.existsByUserIdAndPlanNameAndStatus(
                userId, planName, SubscriptionConstants.SubscriptionStatus.TRIAL);
    }

    private boolean hasActiveTrialSubscription(UUID userId) {
        return subscriptionRepository.existsByUserIdAndStatus(
                userId, SubscriptionConstants.SubscriptionStatus.TRIAL);
    }

    private boolean isPlanAvailableForUser(SubscriptionPlan plan, List<UserSubscription> userHistory,
            UserSubscription currentSubscription) {
        String planName = plan.getName();

        if (SubscriptionConstants.PlanNames.FREE.equals(planName)) {
            return true;
        }

        return !currentSubscription.getPlan().getName().equals(planName);
    }

    private BadRequestException createSubscriptionConstraintException(DataIntegrityViolationException e,
            String message) {
        if (e.getMessage().contains("idx_user_active_subscription")) {
            return new BadRequestException(message);
        }
        return new BadRequestException(
                "Unable to complete subscription operation due to a database constraint. " +
                        "Please try again or contact support.");
    }

    private SubscriptionDto.AvailablePlanResponse createAvailablePlanResponse(
            SubscriptionPlan plan, UUID userId, UserSubscription currentSubscription) {

        String planName = plan.getName();
        boolean isCurrentPlan = currentSubscription.getPlan().getName().equals(planName);
        boolean hasUsedTrial = hasUserUsedTrialForPlan(userId, planName);
        boolean canStartTrial = canUserStartTrialForPlan(userId, planName);

        String restrictionReason = determineRestrictionReason(isCurrentPlan, hasUsedTrial, canStartTrial, userId);

        return subscriptionMapper.toAvailablePlanResponse(
                plan, canStartTrial, hasUsedTrial, isCurrentPlan, restrictionReason);
    }

    private String determineRestrictionReason(boolean isCurrentPlan, boolean hasUsedTrial,
            boolean canStartTrial, UUID userId) {
        if (isCurrentPlan) {
            return "This is your current plan";
        }
        if (!canStartTrial && hasUsedTrial) {
            return "Trial already used for this plan";
        }
        if (hasActiveTrialSubscription(userId)) {
            return "You already have an active trial";
        }
        if (isCurrentPlan && !canStartTrial) {
            return "You cannot start a trial for your current plan";
        }
        return null;
    }
}
