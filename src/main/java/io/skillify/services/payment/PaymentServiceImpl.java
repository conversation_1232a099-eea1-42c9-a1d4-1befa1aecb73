package io.skillify.services.payment;

import java.util.Optional;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.stripe.exception.StripeException;

import io.skillify.exceptions.BadRequestException;
import io.skillify.exceptions.ResourceNotFoundException;
import io.skillify.models.subscription.SubscriptionPlan;
import io.skillify.models.subscription.UserSubscription;
import io.skillify.models.user.User;
import io.skillify.repositories.subscription.SubscriptionPlanRepository;
import io.skillify.repositories.subscription.UserSubscriptionRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
@RequiredArgsConstructor
@Transactional
public class PaymentServiceImpl implements PaymentService {

    private final StripeService stripeService;
    private final SubscriptionPlanRepository planRepository;
    private final UserSubscriptionRepository subscriptionRepository;

    @Value("${app.frontend.success-url}")
    private String defaultReturnUrl;

    @Override
    public com.stripe.model.checkout.Session createCheckoutSession(User user, String planName) {
        SubscriptionPlan plan = findPlanByName(planName);

        validateUserCanSubscribe(user, plan);

        return stripeService.createCheckoutSession(user, plan);
    }

    @Override
    public PaymentService.CheckoutSuccessData processCheckoutSuccess(User user, String sessionId) {
        try {
            com.stripe.model.checkout.Session session = com.stripe.model.checkout.Session.retrieve(sessionId);

            // Validate session belongs to user
            String sessionUserId = session.getMetadata().get("user_id");
            if (!user.getId().toString().equals(sessionUserId)) {
                throw new BadRequestException("Invalid session for this user");
            }

            // Get subscription from session
            if (session.getSubscription() == null) {
                throw new BadRequestException("No subscription found in checkout session");
            }

            Optional<UserSubscription> subscription = subscriptionRepository
                    .findByStripeSubscriptionId(session.getSubscription());

            if (subscription.isEmpty()) {
                throw new ResourceNotFoundException("Subscription not found");
            }

            log.info("Processed checkout success for user {} and subscription {}",
                    user.getId(), subscription.get().getId());

            return new PaymentService.CheckoutSuccessData(session, subscription.get());

        } catch (StripeException e) {
            log.error("Failed to process checkout success for user {}: {}", user.getId(), e.getMessage());
            throw new BadRequestException("Failed to verify checkout: " + e.getUserMessage());
        }
    }

    @Override
    public CancelSubscriptionData cancelSubscription(User user, boolean immediate, String reason) {
        Optional<UserSubscription> subscription = subscriptionRepository.findActiveByUserId(user.getId());

        if (subscription.isEmpty()) {
            throw new BadRequestException("No active subscription found");
        }

        UserSubscription userSub = subscription.get();
        if (userSub.getStripeSubscriptionId() == null) {
            throw new BadRequestException("Cannot cancel subscription without Stripe subscription ID");
        }

        // Cancel in Stripe
        stripeService.cancelSubscription(userSub.getStripeSubscriptionId(), immediate);

        // Update subscription status locally
        if (immediate) {
            userSub.setStatus(io.skillify.constants.SubscriptionConstants.SubscriptionStatus.CANCELLED);
            userSub.markAsEnded();
        } else {
            log.info("Subscription {} marked for cancellation at period end", userSub.getId());
        }
        UserSubscription cancelledSub = subscriptionRepository.saveAndFlush(userSub);

        UserSubscription freeSub = null;
        if (immediate
                && !io.skillify.constants.SubscriptionConstants.PlanNames.FREE.equals(userSub.getPlan().getName())) {
            freeSub = createFreeSubscriptionSafely(user);
        }

        if (reason != null && !reason.isBlank()) {
            log.info("User {} cancelled subscription with reason: {}", user.getId(), reason);
        }

        log.info("Cancelled subscription {} for user {}", userSub.getId(), user.getId());

        return new CancelSubscriptionData(cancelledSub, freeSub);
    }

    private UserSubscription createFreeSubscriptionSafely(User user) {
        try {
            Optional<UserSubscription> existingActive = subscriptionRepository.findActiveByUserId(user.getId());
            if (existingActive.isPresent()) {
                log.warn("User {} still has active subscription {}, skipping free subscription creation",
                        user.getId(), existingActive.get().getId());
                return null;
            }

            var freePlan = planRepository.findByName(io.skillify.constants.SubscriptionConstants.PlanNames.FREE)
                    .orElseThrow(() -> new BadRequestException("Free plan not found"));

            UserSubscription freeSub = io.skillify.models.subscription.UserSubscription.builder()
                    .user(user)
                    .plan(freePlan)
                    .status(io.skillify.constants.SubscriptionConstants.SubscriptionStatus.ACTIVE)
                    .startedAt(java.time.OffsetDateTime.now())
                    .build();

            freeSub = subscriptionRepository.save(freeSub);
            log.info("Created free subscription {} for user {} after cancellation", freeSub.getId(), user.getId());
            return freeSub;

        } catch (org.springframework.dao.DataIntegrityViolationException e) {
            if (e.getMessage().contains("idx_user_active_subscription")) {
                log.warn(
                        "Race condition detected: User {} already has active subscription, skipping free subscription creation",
                        user.getId());
                return null;
            }
            log.error("Failed to create free subscription for user {} due to data integrity violation: {}",
                    user.getId(), e.getMessage());
            throw new BadRequestException("Unable to create free subscription due to existing active subscription");
        } catch (Exception e) {
            log.warn("Failed to create free subscription after cancellation for user {}: {}", user.getId(),
                    e.getMessage());
            return null;
        }
    }

    @Override
    public void syncPlanWithStripe(String planName) {
        SubscriptionPlan plan = findPlanByName(planName);

        StripeService.StripeResourceIds stripeIds = stripeService.syncPlanWithStripe(plan);

        plan.setStripeProductId(stripeIds.productId());
        if (stripeIds.priceId() != null) {
            plan.setStripePriceId(stripeIds.priceId());
        }

        planRepository.save(plan);

        log.info("Updated plan {} in database with Stripe IDs - Product: {}, Price: {}",
                planName, stripeIds.productId(), stripeIds.priceId());
    }

    private SubscriptionPlan findPlanByName(String planName) {
        return planRepository.findByName(planName)
                .orElseThrow(() -> new ResourceNotFoundException("Plan not found: " + planName));
    }

    private void validateUserCanSubscribe(User user, SubscriptionPlan plan) {
        // Check if user already has an active subscription to this plan
        Optional<UserSubscription> existingSubscription = subscriptionRepository.findActiveByUserId(user.getId());

        if (existingSubscription.isPresent()) {
            if (existingSubscription.get().getPlan().getId().equals(plan.getId())) {
                throw new BadRequestException("You already have an active subscription to this plan");
            }
        }
    }
}
