package io.skillify.services.payment;

import io.skillify.models.subscription.UserSubscription;
import io.skillify.models.user.User;

public interface PaymentService {

    /**
     * Creates a checkout session for subscription purchase
     */
    com.stripe.model.checkout.Session createCheckoutSession(User user, String planName);

    /**
     * Processes successful checkout and activates subscription
     */
    CheckoutSuccessData processCheckoutSuccess(User user, String sessionId);

    record CheckoutSuccessData(
            com.stripe.model.checkout.Session session,
            UserSubscription subscription
    ) {}

    /**
     * Cancels user's active subscription
     */
    CancelSubscriptionData cancelSubscription(User user, boolean immediate, String reason);

    record CancelSubscriptionData(
            UserSubscription cancelledSubscription,
            UserSubscription freePlanSubscription
    ) {}

    /**
     * Synchronizes subscription plan with Stripe
     */
    void syncPlanWithStripe(String planName);
}
