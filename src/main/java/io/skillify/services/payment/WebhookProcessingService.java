package io.skillify.services.payment;

import java.time.Instant;
import java.time.OffsetDateTime;
import java.time.ZoneOffset;
import java.util.Optional;
import java.util.UUID;

import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.stripe.model.Event;
import com.stripe.model.Invoice;
import com.stripe.model.Subscription;
import com.stripe.model.checkout.Session;
import com.stripe.net.Webhook;

import io.skillify.constants.SubscriptionConstants;
import io.skillify.models.subscription.PaymentEvent;
import io.skillify.models.subscription.SubscriptionPlan;
import io.skillify.models.subscription.UserSubscription;
import io.skillify.models.user.User;
import io.skillify.repositories.subscription.PaymentEventRepository;
import io.skillify.repositories.subscription.SubscriptionPlanRepository;
import io.skillify.repositories.subscription.UserSubscriptionRepository;
import io.skillify.repositories.user.UserRepository;
import io.skillify.services.subscription.SubscriptionService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
@RequiredArgsConstructor
@Transactional
public class WebhookProcessingService {

    private final PaymentEventRepository paymentEventRepository;
    private final UserSubscriptionRepository subscriptionRepository;
    private final SubscriptionPlanRepository planRepository;
    private final UserRepository userRepository;
    private final SubscriptionService subscriptionService;
    private final ObjectMapper objectMapper;

    public boolean processWebhook(String payload, String signature, String webhookSecret) {
        try {
            Event event = Webhook.constructEvent(payload, signature, webhookSecret, 300L);

            log.info("Received Stripe webhook: {} with ID: {}", event.getType(), event.getId());

            processEventAsync(event);

            return true;
        } catch (Exception e) {
            log.error("Failed to process webhook: {}", e.getMessage(), e);
            return false;
        }
    }

    @Async
    @Transactional
    public void processEventAsync(Event event) {
        if (paymentEventRepository.existsByStripeEventId(event.getId())) {
            log.info("Event {} already processed", event.getId());
            return;
        }

        PaymentEvent paymentEvent = PaymentEvent.builder()
                .stripeEventId(event.getId())
                .eventType(event.getType())
                .rawData(objectMapper.valueToTree(event.toJson()))
                .processed(false)
                .build();

        paymentEventRepository.save(paymentEvent);

        try {
            switch (event.getType()) {
                case "checkout.session.completed" -> handleCheckoutCompleted(event, paymentEvent);
                case "checkout.session.expired" -> handleCheckoutExpired(event, paymentEvent);
                case "customer.subscription.created" -> handleSubscriptionCreated(event, paymentEvent);
                case "customer.subscription.updated" -> handleSubscriptionUpdated(event, paymentEvent);
                case "customer.subscription.deleted" -> handleSubscriptionCanceled(event, paymentEvent);
                case "invoice.payment_succeeded" -> handlePaymentSucceeded(event, paymentEvent);
                case "invoice.payment_failed" -> handlePaymentFailed(event, paymentEvent);

                default -> log.info("Unhandled event type: {}", event.getType());
            }

            paymentEvent.markAsProcessed();
            paymentEventRepository.save(paymentEvent);

        } catch (Exception e) {
            log.error("Error processing webhook event {}: {}", event.getId(), e.getMessage(), e);
            paymentEvent.recordError(e.getMessage());
            paymentEventRepository.save(paymentEvent);
        }
    }

    private void handleCheckoutCompleted(Event event, PaymentEvent paymentEvent) {
        Session session = (Session) event.getDataObjectDeserializer().getObject().orElse(null);
        if (session == null) {
            log.error("No session data in checkout.session.completed event");
            return;
        }

        String userIdStr = session.getMetadata().get("user_id");
        String planIdStr = session.getMetadata().get("plan_id");

        if (userIdStr == null || planIdStr == null) {
            log.error("Missing required metadata in checkout session {}: user_id={}, plan_id={}",
                    session.getId(), userIdStr, planIdStr);
            return;
        }

        try {
            UUID userId = UUID.fromString(userIdStr);
            UUID planId = UUID.fromString(planIdStr);

            // Get user and plan references
            User user = userRepository.getReferenceById(userId);
            SubscriptionPlan plan = planRepository.getReferenceById(planId);

            Optional<UserSubscription> existingByCustomer = subscriptionRepository
                    .findByStripeCustomerId(session.getCustomer())
                    .stream()
                    .findFirst();

            if (existingByCustomer.isPresent()) {
                log.info("Found existing subscription {} for customer {}, updating it",
                        existingByCustomer.get().getId(), session.getCustomer());
                UserSubscription existing = existingByCustomer.get();

                // Update the subscription to the new plan
                existing.setPlan(plan);
                existing.setStripeCustomerId(session.getCustomer());
                existing.setStatus(SubscriptionConstants.SubscriptionStatus.ACTIVE);
                existing.setStartedAt(OffsetDateTime.now());
                existing.setEndedAt(null);

                subscriptionRepository.save(existing);
                paymentEvent.setSubscription(existing);
                return;
            }

            // Check if subscription already exists for this user
            Optional<UserSubscription> existingByUser = subscriptionRepository.findActiveByUserId(userId);
            if (existingByUser.isPresent()) {
                log.info("User {} already has active subscription {}, updating with checkout data",
                        userId, existingByUser.get().getId());
                UserSubscription existing = existingByUser.get();

                existing.setPlan(plan);
                existing.setStripeCustomerId(session.getCustomer());
                existing.setStatus(SubscriptionConstants.SubscriptionStatus.ACTIVE);
                existing.setStartedAt(OffsetDateTime.now());
                existing.setEndedAt(null);

                subscriptionRepository.save(existing);
                paymentEvent.setSubscription(existing);
                return;
            }

            // No existing subscription found, create new one
            try {
                UserSubscription newSubscription = UserSubscription.builder()
                        .user(user)
                        .plan(plan)
                        .status(SubscriptionConstants.SubscriptionStatus.ACTIVE)
                        .stripeCustomerId(session.getCustomer())
                        .startedAt(OffsetDateTime.now())
                        .build();

                subscriptionRepository.save(newSubscription);
                paymentEvent.setSubscription(newSubscription);

                log.info("Created subscription {} for user {} on plan {} from checkout completion",
                        newSubscription.getId(), userId, plan.getName());

            } catch (org.springframework.dao.DataIntegrityViolationException ex) {
                log.warn("Constraint violation when creating subscription for user {}, likely due to concurrent webhook processing. Retrying lookup...", userId);

                Optional<UserSubscription> retryExisting = subscriptionRepository.findActiveByUserId(userId);
                if (retryExisting.isPresent()) {
                    log.info("Found subscription {} created by concurrent webhook, updating with checkout data",
                            retryExisting.get().getId());
                    UserSubscription existing = retryExisting.get();

                    existing.setPlan(plan);
                    existing.setStripeCustomerId(session.getCustomer());
                    existing.setStatus(SubscriptionConstants.SubscriptionStatus.ACTIVE);
                    existing.setEndedAt(null); // Clear ended_at when reactivating subscription

                    subscriptionRepository.save(existing);
                    paymentEvent.setSubscription(existing);
                } else {
                    log.error("Failed to find subscription after constraint violation for user {}", userId);
                    throw new RuntimeException("Unable to handle concurrent subscription creation", ex);
                }
            }

        } catch (Exception e) {
            log.error("Error processing checkout completion for session {}: {}", session.getId(), e.getMessage(), e);
            throw new RuntimeException("Failed to process checkout completion", e);
        }
    }

    private void handleCheckoutExpired(Event event, PaymentEvent paymentEvent) {
        Session session = (Session) event.getDataObjectDeserializer().getObject().orElse(null);
        if (session == null) {
            log.error("No session data in checkout.session.expired event");
            return;
        }

        log.info("Checkout session {} expired for customer {}",
                session.getId(), session.getCustomer());

        String userIdStr = session.getMetadata() != null ? session.getMetadata().get("user_id") : null;
        String planIdStr = session.getMetadata() != null ? session.getMetadata().get("plan_id") : null;

        if (userIdStr != null && planIdStr != null) {
            log.info("Expired checkout was for user {} attempting to subscribe to plan {}",
                    userIdStr, planIdStr);
        }
    }

    private void handleSubscriptionCreated(Event event, PaymentEvent paymentEvent) {
        Subscription stripeSubscription = (Subscription) event.getDataObjectDeserializer().getObject().orElse(null);
        if (stripeSubscription == null) {
            log.error("No subscription data in customer.subscription.created event");
            return;
        }

        try {
            Optional<UserSubscription> subscriptionOpt = subscriptionRepository
                    .findByStripeSubscriptionId(stripeSubscription.getId());

            UserSubscription userSubscription;

            if (subscriptionOpt.isPresent()) {
                log.info("Found existing subscription {} to update from Stripe subscription.created event.",
                        subscriptionOpt.get().getId());
                userSubscription = subscriptionOpt.get();
            } else {
                log.warn("UserSubscription with Stripe ID {} not found. Creating from webhook. " +
                        "This happens when 'subscription.created' arrives before 'checkout.session.completed'.",
                        stripeSubscription.getId());

                String userIdStr = stripeSubscription.getMetadata().get("user_id");
                String planName = stripeSubscription.getMetadata().get("plan_name");

                if (userIdStr == null || planName == null) {
                    log.error("Missing required metadata (user_id, plan_name) on Stripe subscription {}. " +
                            "Cannot create local record. Metadata: {}",
                            stripeSubscription.getId(), stripeSubscription.getMetadata());
                    throw new RuntimeException("Stripe subscription is missing required metadata");
                }

                UUID userId = UUID.fromString(userIdStr);
                User user = userRepository.getReferenceById(userId);
                SubscriptionPlan plan = planRepository.findByName(planName)
                        .orElseThrow(() -> new RuntimeException("Plan not found from webhook metadata: " + planName));

                // Check if user already has an active subscription
                Optional<UserSubscription> existingActiveSubscription = subscriptionRepository.findActiveByUserId(userId);

                if (existingActiveSubscription.isPresent()) {
                    // User has an existing active subscription - upgrade it instead of creating new one
                    log.info("User {} has existing active subscription {}, upgrading to plan {}",
                            userId, existingActiveSubscription.get().getId(), planName);

                    userSubscription = existingActiveSubscription.get();

                    // Update the existing subscription to the new plan
                    userSubscription.setPlan(plan);
                    userSubscription.setStripeCustomerId(stripeSubscription.getCustomer());
                    userSubscription.setStatus(SubscriptionConstants.SubscriptionStatus.ACTIVE);
                    userSubscription.setStartedAt(OffsetDateTime.now());
                    userSubscription.setEndedAt(null);

                    log.info("Upgraded existing subscription {} to plan {} with Stripe subscription {}",
                            userSubscription.getId(), planName, stripeSubscription.getId());
                } else {
                    // No existing subscription - create new one
                    userSubscription = UserSubscription.builder()
                            .user(user)
                            .plan(plan)
                            .stripeCustomerId(stripeSubscription.getCustomer())
                            .status(SubscriptionConstants.SubscriptionStatus.ACTIVE)
                            .startedAt(OffsetDateTime.now())
                            .build();

                    log.info("Created new subscription from webhook for user {} on plan {}", userId, planName);
                }
            }

            updateSubscriptionFromStripe(userSubscription, stripeSubscription);
            paymentEvent.setSubscription(userSubscription);

            log.info("Upserted subscription {} for Stripe subscription {}",
                    userSubscription.getId(), stripeSubscription.getId());

        } catch (Exception e) {
            log.error("Error handling subscription creation for Stripe subscription {}: {}",
                    stripeSubscription.getId(), e.getMessage(), e);
            throw new RuntimeException("Failed to handle subscription creation", e);
        }
    }

    private void handleSubscriptionUpdated(Event event, PaymentEvent paymentEvent) {
        Subscription stripeSubscription = (Subscription) event.getDataObjectDeserializer().getObject().orElse(null);
        if (stripeSubscription == null) {
            log.warn("No subscription data in customer.subscription.updated event");
            return;
        }

        Optional<UserSubscription> userSubscriptionOpt = subscriptionRepository
                .findByStripeSubscriptionId(stripeSubscription.getId());

        if (userSubscriptionOpt.isPresent()) {
            UserSubscription userSubscription = userSubscriptionOpt.get();
            updateSubscriptionFromStripe(userSubscription, stripeSubscription);
            paymentEvent.setSubscription(userSubscription);
            log.info("Updated subscription {} status from Stripe", userSubscription.getId());
        } else {
            log.warn("Could not find user subscription for Stripe subscription {}", stripeSubscription.getId());
        }
    }

    private void handleSubscriptionCanceled(Event event, PaymentEvent paymentEvent) {
        Subscription stripeSubscription = (Subscription) event.getDataObjectDeserializer().getObject().orElse(null);
        if (stripeSubscription == null) {
            log.warn("No subscription data in customer.subscription.deleted event");
            return;
        }

        Optional<UserSubscription> userSubscriptionOpt = subscriptionRepository
                .findByStripeSubscriptionId(stripeSubscription.getId());

        if (userSubscriptionOpt.isPresent()) {
            UserSubscription userSubscription = userSubscriptionOpt.get();
            userSubscription.setStatus(SubscriptionConstants.SubscriptionStatus.CANCELLED);
            userSubscription.markAsEnded();
            subscriptionRepository.save(userSubscription);

            subscriptionService.createDefaultFreeSubscription(userSubscription.getUser().getId());

            paymentEvent.setSubscription(userSubscription);
            log.info("Cancelled subscription {} and created free subscription for user {}",
                    userSubscription.getId(), userSubscription.getUser().getId());
        } else {
            log.warn("Could not find user subscription for cancelled Stripe subscription {}",
                    stripeSubscription.getId());
        }
    }

    private void handlePaymentSucceeded(Event event, PaymentEvent paymentEvent) {
        Invoice invoice = (Invoice) event.getDataObjectDeserializer().getObject().orElse(null);
        if (invoice == null) {
            log.warn("No invoice data in invoice.payment_succeeded event");
            return;
        }

        String subscriptionId = extractSubscriptionIdFromInvoice(invoice);
        if (subscriptionId == null) {
            log.warn("No subscription data in invoice.payment_succeeded event");
            return;
        }
        Optional<UserSubscription> userSubscriptionOpt = subscriptionRepository
                .findByStripeSubscriptionId(subscriptionId);

        if (userSubscriptionOpt.isPresent()) {
            UserSubscription userSubscription = userSubscriptionOpt.get();
            if (!SubscriptionConstants.SubscriptionStatus.ACTIVE.equals(userSubscription.getStatus())) {
                userSubscription.setStatus(SubscriptionConstants.SubscriptionStatus.ACTIVE);
                if (userSubscription.getEndedAt() != null) {
                    userSubscription.setEndedAt(null);
                    userSubscription.setStartedAt(OffsetDateTime.now());
                }
                subscriptionRepository.save(userSubscription);
                log.info("Activated subscription {} after successful payment", userSubscription.getId());
            }
            paymentEvent.setSubscription(userSubscription);
        } else {
            log.warn("Could not find user subscription for payment succeeded event, subscription ID: {}",
                    subscriptionId);
        }
    }

    private void handlePaymentFailed(Event event, PaymentEvent paymentEvent) {
        Invoice invoice = (Invoice) event.getDataObjectDeserializer().getObject().orElse(null);
        if (invoice == null) {
            log.warn("No invoice data in invoice.payment_failed event");
            return;
        }

        String subscriptionId = extractSubscriptionIdFromInvoice(invoice);
        if (subscriptionId == null) {
            log.warn("No subscription data in invoice.payment_failed event");
            return;
        }
        Optional<UserSubscription> userSubscriptionOpt = subscriptionRepository
                .findByStripeSubscriptionId(subscriptionId);

        if (userSubscriptionOpt.isPresent()) {
            UserSubscription userSubscription = userSubscriptionOpt.get();
            userSubscription.setStatus(SubscriptionConstants.SubscriptionStatus.SUSPENDED);
            subscriptionRepository.save(userSubscription);

            paymentEvent.setSubscription(userSubscription);
            log.warn("Suspended subscription {} due to payment failure", userSubscription.getId());
        } else {
            log.warn("Could not find user subscription for payment failed event, subscription ID: {}",
                    subscriptionId);
        }
    }



    private void updateSubscriptionFromStripe(UserSubscription subscription, Subscription stripeSubscription) {
        subscription.setStripeSubscriptionId(stripeSubscription.getId());
        subscription.setStripeCustomerId(stripeSubscription.getCustomer());

        Long billingCycleAnchor = stripeSubscription.getBillingCycleAnchor();
        if (billingCycleAnchor != null) {
            subscription.setCurrentPeriodStart(
                    OffsetDateTime.ofInstant(Instant.ofEpochSecond(billingCycleAnchor), ZoneOffset.UTC));
            subscription.setCurrentPeriodEnd(
                    OffsetDateTime.ofInstant(Instant.ofEpochSecond(billingCycleAnchor), ZoneOffset.UTC).plusMonths(1));
        }

        if (stripeSubscription.getTrialEnd() != null) {
            subscription.setTrialEnd(
                    OffsetDateTime.ofInstant(Instant.ofEpochSecond(stripeSubscription.getTrialEnd()), ZoneOffset.UTC));
        }

        String newStatus = mapStripeStatus(stripeSubscription.getStatus());
        subscription.setStatus(newStatus);

        if (SubscriptionConstants.SubscriptionStatus.ACTIVE.equals(newStatus) && subscription.getEndedAt() != null) {
            subscription.setEndedAt(null);
            subscription.setStartedAt(OffsetDateTime.now());
        }

        subscriptionRepository.save(subscription);
    }

    private String mapStripeStatus(String stripeStatus) {
        return switch (stripeStatus) {
            case "active" -> SubscriptionConstants.SubscriptionStatus.ACTIVE;
            case "canceled" -> SubscriptionConstants.SubscriptionStatus.CANCELLED;
            case "past_due" -> SubscriptionConstants.SubscriptionStatus.SUSPENDED;
            case "trialing" -> SubscriptionConstants.SubscriptionStatus.TRIAL;
            case "incomplete", "incomplete_expired" -> SubscriptionConstants.SubscriptionStatus.SUSPENDED;
            default -> {
                log.warn("Unknown Stripe status: {}, defaulting to SUSPENDED", stripeStatus);
                yield SubscriptionConstants.SubscriptionStatus.SUSPENDED;
            }
        };
    }

    private String extractSubscriptionIdFromInvoice(Invoice invoice) {
        try {
            String jsonString = invoice.toJson();
            if (jsonString != null && jsonString.contains("\"subscription\":")) {
                int subscriptionStart = jsonString.indexOf("\"subscription\":\"");
                if (subscriptionStart != -1) {
                    int valueStart = subscriptionStart + "\"subscription\":\"".length();
                    int valueEnd = jsonString.indexOf("\"", valueStart);
                    if (valueEnd != -1) {
                        return jsonString.substring(valueStart, valueEnd);
                    }
                }
                if (jsonString.contains("\"subscription\":null")) {
                    return null;
                }
            }

            if (invoice.getCustomer() != null) {
                log.debug("No subscription ID in invoice, trying to find by customer ID: {}", invoice.getCustomer());
                Optional<UserSubscription> userSub = subscriptionRepository
                        .findByStripeCustomerId(invoice.getCustomer())
                        .stream()
                        .filter(sub -> SubscriptionConstants.SubscriptionStatus.ACTIVE.equals(sub.getStatus()))
                        .findFirst();

                if (userSub.isPresent() && userSub.get().getStripeSubscriptionId() != null) {
                    return userSub.get().getStripeSubscriptionId();
                }
            }
        } catch (Exception e) {
            log.warn("Failed to extract subscription ID from invoice: {}", e.getMessage());
        }
        return null;
    }

}
