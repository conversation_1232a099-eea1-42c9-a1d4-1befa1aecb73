package io.skillify.services.payment;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.stripe.exception.StripeException;
import com.stripe.model.Customer;
import com.stripe.model.Price;
import com.stripe.model.Product;
import com.stripe.model.Subscription;
import com.stripe.model.checkout.Session;
import com.stripe.param.CustomerCreateParams;
import com.stripe.param.CustomerSearchParams;
import com.stripe.param.PriceCreateParams;
import com.stripe.param.ProductCreateParams;
import com.stripe.param.SubscriptionCancelParams;
import com.stripe.param.SubscriptionUpdateParams;
import com.stripe.param.checkout.SessionCreateParams;

import io.skillify.exceptions.BadRequestException;
import io.skillify.models.subscription.SubscriptionPlan;
import io.skillify.models.user.User;
import io.skillify.repositories.subscription.UserSubscriptionRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
@RequiredArgsConstructor
@Transactional
public class StripeService {

    private final UserSubscriptionRepository subscriptionRepository;

    @Value("${app.frontend.success-url}")
    private String successUrl;

    @Value("${app.frontend.cancel-url}")
    private String cancelUrl;

    @Cacheable(value = "stripe-customers", key = "#user.id")
    public Customer getOrCreateCustomer(User user) {
        try {
            var existingSubscription = subscriptionRepository.findActiveByUserId(user.getId());
            if (existingSubscription.isPresent() && existingSubscription.get().getStripeCustomerId() != null) {
                return Customer.retrieve(existingSubscription.get().getStripeCustomerId());
            }

            var searchParams = CustomerSearchParams.builder()
                    .setQuery(String.format("email:'%s'", user.getEmail()))
                    .build();

            var searchResult = Customer.search(searchParams);
            if (!searchResult.getData().isEmpty()) {
                log.info("Found existing Stripe customer {} for user {}",
                        searchResult.getData().get(0).getId(), user.getId());
                return searchResult.getData().get(0);
            }

            // Create new customer
            var params = CustomerCreateParams.builder()
                    .setEmail(user.getEmail())
                    .setName(user.getUsername())
                    .putMetadata("user_id", user.getId().toString())
                    .putMetadata("skillify_user", "true")
                    .build();

            var customer = Customer.create(params);
            log.info("Created Stripe customer {} for user {}", customer.getId(), user.getId());
            return customer;

        } catch (StripeException e) {
            log.error("Failed to create/retrieve Stripe customer for user {}: {}", user.getId(), e.getMessage());
            throw new BadRequestException("Failed to process customer information: " + e.getUserMessage());
        }
    }

    public Session createCheckoutSession(User user, SubscriptionPlan plan) {
        validatePlanForPayment(plan);

        try {
            Customer customer = getOrCreateCustomer(user);
            SessionCreateParams.Builder sessionBuilder = createBaseCheckoutSession(customer, plan, user);

            if (plan.requiresPayment()) {
                sessionBuilder.setSubscriptionData(
                        SessionCreateParams.SubscriptionData.builder()
                                .putMetadata("plan_name", plan.getName())
                                .putMetadata("user_id", user.getId().toString())
                                .build());
            }

            Session session = Session.create(sessionBuilder.build());
            log.info("Created checkout session {} for user {} and plan {}",
                    session.getId(), user.getId(), plan.getName());

            return session;

        } catch (StripeException e) {
            log.error("Failed to create checkout session for user {} and plan {}: {}",
                    user.getId(), plan.getName(), e.getMessage());
            throw new BadRequestException("Failed to create payment session: " + e.getUserMessage());
        }
    }

    /**
     * Creates or updates Stripe product and price for a subscription plan
     */
    public StripeResourceIds syncPlanWithStripe(SubscriptionPlan plan) {
        try {
            Product product = createOrUpdateProduct(plan);
            String productId = product.getId();
            String priceId = null;

            if (plan.requiresPayment()) {
                Price price = createPrice(product, plan);
                priceId = price.getId();
                log.info("Synced plan {} with Stripe - Product: {}, Price: {}",
                        plan.getName(), productId, priceId);
            } else {
                log.info("Synced free plan {} with Stripe - Product: {}", plan.getName(), productId);
            }

            return new StripeResourceIds(productId, priceId);

        } catch (StripeException e) {
            log.error("Failed to sync plan {} with Stripe: {}", plan.getName(), e.getMessage());
            throw new BadRequestException("Failed to configure payment plan: " + e.getUserMessage());
        }
    }

    /**
     * Record class to hold Stripe resource IDs
     */
    public static record StripeResourceIds(String productId, String priceId) {}

    public Subscription cancelSubscription(String stripeSubscriptionId, boolean immediate) {
        try {
            var params = SubscriptionCancelParams.builder()
                    .setInvoiceNow(false)
                    .setProrate(!immediate)
                    .build();

            var subscription = Subscription.retrieve(stripeSubscriptionId);
            if (immediate) {
                subscription = subscription.cancel(params);
            } else {
                // Cancel at period end
                var updateParams = SubscriptionUpdateParams.builder()
                        .setCancelAtPeriodEnd(true)
                        .build();
                subscription = subscription.update(updateParams);
            }

            log.info("Cancelled Stripe subscription {} (immediate: {})", stripeSubscriptionId, immediate);
            return subscription;

        } catch (StripeException e) {
            log.error("Failed to cancel Stripe subscription {}: {}", stripeSubscriptionId, e.getMessage());
            throw new BadRequestException("Failed to cancel subscription: " + e.getUserMessage());
        }
    }

    public Subscription updateSubscription(String stripeSubscriptionId, SubscriptionPlan newPlan) {
        validatePlanForPayment(newPlan);

        try {
            var subscription = Subscription.retrieve(stripeSubscriptionId);
            var currentItem = subscription.getItems().getData().get(0);

            var params = SubscriptionUpdateParams.builder()
                    .addItem(
                            SubscriptionUpdateParams.Item.builder()
                                    .setId(currentItem.getId())
                                    .setPrice(newPlan.getStripePriceId())
                                    .build())
                    .setProrationBehavior(SubscriptionUpdateParams.ProrationBehavior.CREATE_PRORATIONS)
                    .putMetadata("plan_name", newPlan.getName())
                    .build();

            subscription = subscription.update(params);
            log.info("Updated Stripe subscription {} to plan {}", stripeSubscriptionId, newPlan.getName());

            return subscription;

        } catch (StripeException e) {
            log.error("Failed to update Stripe subscription {} to plan {}: {}",
                    stripeSubscriptionId, newPlan.getName(), e.getMessage());
            throw new BadRequestException("Failed to update subscription: " + e.getUserMessage());
        }
    }

    public Subscription getSubscription(String stripeSubscriptionId) {
        try {
            return Subscription.retrieve(stripeSubscriptionId);
        } catch (StripeException e) {
            log.error("Failed to retrieve Stripe subscription {}: {}", stripeSubscriptionId, e.getMessage());
            throw new BadRequestException("Failed to retrieve subscription: " + e.getUserMessage());
        }
    }

    private void validatePlanForPayment(SubscriptionPlan plan) {
        if (plan.requiresPayment() && plan.getStripePriceId() == null) {
            throw new BadRequestException("Plan " + plan.getName() + " is not configured for payments");
        }
    }

    private SessionCreateParams.Builder createBaseCheckoutSession(Customer customer, SubscriptionPlan plan, User user) {
        var builder = SessionCreateParams.builder()
                .setMode(SessionCreateParams.Mode.SUBSCRIPTION)
                .setCustomer(customer.getId())
                .setSuccessUrl(successUrl + "?session_id={CHECKOUT_SESSION_ID}")
                .setCancelUrl(cancelUrl)
                .putMetadata("user_id", user.getId().toString())
                .putMetadata("plan_id", plan.getId().toString())
                .putMetadata("plan_name", plan.getName());

        if (plan.requiresPayment()) {
            builder.addLineItem(
                    SessionCreateParams.LineItem.builder()
                            .setPrice(plan.getStripePriceId())
                            .setQuantity(1L)
                            .build());
        }

        return builder;
    }

    private Product createOrUpdateProduct(SubscriptionPlan plan) throws StripeException {
        if (plan.getStripeProductId() != null) {
            try {
                var existingProduct = Product.retrieve(plan.getStripeProductId());
                // Update product if needed
                return existingProduct;
            } catch (StripeException e) {
                log.warn("Existing product {} not found, creating new one", plan.getStripeProductId());
            }
        }

        var params = ProductCreateParams.builder()
                .setName(plan.getDisplayName())
                .setDescription(String.format("Skillify %s subscription - %s organizations",
                        plan.getDisplayName(), plan.getMaxOrganizations()))
                .putMetadata("plan_id", plan.getId().toString())
                .putMetadata("plan_name", plan.getName())
                .putMetadata("skillify_plan", "true")
                .build();

        return Product.create(params);
    }

    private Price createPrice(Product product, SubscriptionPlan plan) throws StripeException {
        if (plan.getStripePriceId() != null) {
            try {
                return Price.retrieve(plan.getStripePriceId());
            } catch (StripeException e) {
                log.warn("Existing price {} not found, creating new one", plan.getStripePriceId());
            }
        }

        var params = PriceCreateParams.builder()
                .setProduct(product.getId())
                .setUnitAmount(plan.getPriceMonthlyCents().longValue())
                .setCurrency("usd")
                .setRecurring(
                        PriceCreateParams.Recurring.builder()
                                .setInterval(PriceCreateParams.Recurring.Interval.MONTH)
                                .build())
                .setNickname(plan.getName() + "_monthly")
                .putMetadata("plan_id", plan.getId().toString())
                .putMetadata("plan_name", plan.getName())
                .build();

        return Price.create(params);
    }
}
