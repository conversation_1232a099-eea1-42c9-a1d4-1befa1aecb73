package io.skillify.services.payment;

import java.time.OffsetDateTime;

import org.springframework.cache.annotation.CacheEvict;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import io.skillify.constants.SubscriptionConstants;
import io.skillify.models.subscription.PaymentEvent;
import io.skillify.repositories.subscription.PaymentEventRepository;
import io.skillify.repositories.subscription.SubscriptionPlanRepository;
import io.skillify.repositories.subscription.UserSubscriptionRepository;
import io.skillify.services.subscription.SubscriptionService;
import lombok.Builder;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;


@Slf4j
@Service
@RequiredArgsConstructor
@Transactional
public class PaymentMaintenanceService {

    private final PaymentEventRepository paymentEventRepository;
    private final UserSubscriptionRepository subscriptionRepository;
    private final SubscriptionPlanRepository planRepository;
    private final SubscriptionService subscriptionService;
    private final StripeService stripeService;

    /**
     * Cleanup old processed payment events (runs daily at 2 AM)
     */
    @Scheduled(cron = "0 0 2 * * *")
    @Transactional
    public void cleanupOldPaymentEvents() {
        var cutoffTime = OffsetDateTime.now().minusDays(90);
        var oldEvents = paymentEventRepository.findOldProcessedEvents(cutoffTime);

        if (!oldEvents.isEmpty()) {
            paymentEventRepository.deleteAll(oldEvents);
            log.info("Cleaned up {} old payment events", oldEvents.size());
        }
    }

    /**
     * Retry failed webhook processing (runs every 30 minutes)
     */
    @Scheduled(fixedDelay = 1800000)
    @Transactional
    public void retryFailedWebhookEvents() {
        var failedEvents = paymentEventRepository.findUnprocessedEventsForRetry();

        if (failedEvents.isEmpty()) {
            return;
        }

        log.info("Retrying {} failed webhook events", failedEvents.size());

        for (PaymentEvent event : failedEvents) {
            try {
                processFailedEvent(event);
                event.markAsProcessed();
                paymentEventRepository.save(event);
                log.info("Successfully retried event {}", event.getStripeEventId());

            } catch (Exception e) {
                event.recordError("Retry failed: " + e.getMessage());
                paymentEventRepository.save(event);
                log.warn("Retry failed for event {}: {}", event.getStripeEventId(), e.getMessage());
            }
        }
    }

    /**
     * Check for expired subscriptions (runs every hour)
     */
    @Scheduled(fixedRate = 3600000)
    @CacheEvict(value = "user-subscriptions", allEntries = true)
    @Transactional
    public void processExpiredSubscriptions() {
        var now = OffsetDateTime.now();

        // Handle expired Stripe subscriptions
        var expiredStripeSubscriptions = subscriptionRepository.findExpiredStripeSubscriptions(now);
        for (var subscription : expiredStripeSubscriptions) {
            try {
                subscription.setStatus(SubscriptionConstants.SubscriptionStatus.EXPIRED);
                subscription.markAsEnded();
                subscriptionRepository.save(subscription);

                // Create free subscription
                subscriptionService.createDefaultFreeSubscription(subscription.getUser().getId());

                log.info("Processed expired Stripe subscription for user {}",
                    subscription.getUser().getId());

            } catch (Exception e) {
                log.error("Failed to process expired subscription {}: {}",
                    subscription.getId(), e.getMessage());
            }
        }

        // Handle expired trial subscriptions
        var expiredTrials = subscriptionRepository.findExpiredTrialSubscriptions(now);
        for (var subscription : expiredTrials) {
            try {
                // If it's a Stripe trial, let Stripe handle it
                if (subscription.isStripeManaged()) {
                    continue;
                }

                subscription.setStatus(SubscriptionConstants.SubscriptionStatus.EXPIRED);
                subscription.markAsEnded();
                subscriptionRepository.save(subscription);

                // Create free subscription
                subscriptionService.createDefaultFreeSubscription(subscription.getUser().getId());

                log.info("Processed expired trial subscription for user {}",
                    subscription.getUser().getId());

            } catch (Exception e) {
                log.error("Failed to process expired trial {}: {}",
                    subscription.getId(), e.getMessage());
            }
        }
    }

    /**
     * Sync subscription plans with Stripe (runs daily at 3 AM)
     */
    @Scheduled(cron = "0 0 3 * * *")
    @Transactional
    public void syncPlansWithStripe() {
        var plans = planRepository.findByIsActiveTrue();

        for (var plan : plans) {
            try {
                if (plan.requiresPayment() && !plan.isStripeConfigured()) {
                    log.info("Syncing plan {} with Stripe", plan.getName());
                    stripeService.syncPlanWithStripe(plan);
                }
            } catch (Exception e) {
                log.error("Failed to sync plan {} with Stripe: {}", plan.getName(), e.getMessage());
            }
        }
    }

    /**
     * Generate subscription metrics report (runs daily at 1 AM)
     */
    @Scheduled(cron = "0 0 1 * * *")
    @Transactional(readOnly = true)
    public void generateMetricsReport() {
        try {
            var totalSubscriptions = subscriptionRepository.count();
            var activeSubscriptions = subscriptionRepository.countByStatus(SubscriptionConstants.SubscriptionStatus.ACTIVE);
            var trialSubscriptions = subscriptionRepository.countByStatus(SubscriptionConstants.SubscriptionStatus.TRIAL);
            var unprocessedEvents = paymentEventRepository.countUnprocessedEvents();

            log.info("Daily Metrics Report - Total: {}, Active: {}, Trials: {}, Unprocessed Events: {}",
                totalSubscriptions, activeSubscriptions, trialSubscriptions, unprocessedEvents);

        } catch (Exception e) {
            log.error("Failed to generate metrics report: {}", e.getMessage());
        }
    }

    /**
     * Manual cleanup trigger for admin operations
     */
    public long manualCleanupPaymentEvents(int daysOld) {
        var cutoffTime = OffsetDateTime.now().minusDays(daysOld);
        var oldEvents = paymentEventRepository.findOldProcessedEvents(cutoffTime);
        var count = oldEvents.size();

        if (!oldEvents.isEmpty()) {
            paymentEventRepository.deleteAll(oldEvents);
            log.info("Manual cleanup removed {} payment events older than {} days", count, daysOld);
        }

        return count;
    }

    /**
     * Get payment processing health status
     */
    public PaymentHealthStatus getPaymentHealthStatus() {
        var unprocessedCount = paymentEventRepository.countUnprocessedEvents();
        var recentFailures = paymentEventRepository.findByEventTypeAndProcessedFalse("invoice.payment_failed").size();

        return PaymentHealthStatus.builder()
            .unprocessedEvents(unprocessedCount)
            .recentPaymentFailures(recentFailures)
            .healthy(unprocessedCount < 10 && recentFailures < 5)
            .build();
    }

    /**
     * Force retry specific payment event
     */
    @Transactional
    public boolean retryPaymentEvent(String stripeEventId) {
        var event = paymentEventRepository.findByStripeEventId(stripeEventId)
            .orElse(null);

        if (event == null || event.isProcessed() || !event.canRetry()) {
            return false;
        }

        try {
            processFailedEvent(event);
            event.markAsProcessed();
            paymentEventRepository.save(event);
            log.info("Manually retried event {}", stripeEventId);
            return true;

        } catch (Exception e) {
            event.recordError("Manual retry failed: " + e.getMessage());
            paymentEventRepository.save(event);
            log.error("Manual retry failed for event {}: {}", stripeEventId, e.getMessage());
            return false;
        }
    }

    private void processFailedEvent(PaymentEvent event) {
        log.info("Processing failed event {} of type {}", event.getStripeEventId(), event.getEventType());

        if (!event.canRetry()) {
            throw new RuntimeException("Event exceeded retry limit");
        }
    }

    @Builder
    @Data
    public static class PaymentHealthStatus {
        private long unprocessedEvents;
        private long recentPaymentFailures;
        private boolean healthy;
    }
}
