package io.skillify.services.org;

import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import io.skillify.dtos.ResponseDto;
import io.skillify.dtos.org.OrgDto;
import io.skillify.exceptions.SubscriptionLimitException;
import io.skillify.mappers.OrgMapper;
import io.skillify.models.org.OrgMember;
import io.skillify.models.org.Organization;
import io.skillify.models.org.OrganizationRole;
import io.skillify.models.user.User;
import io.skillify.repositories.org.OrgMemberRepository;
import io.skillify.repositories.org.OrgRoleRepository;
import io.skillify.repositories.org.OrganizationRepository;
import io.skillify.repositories.user.UserRepository;
import io.skillify.models.subscription.SubscriptionStatus;
import io.skillify.services.subscription.SubscriptionService;
import lombok.RequiredArgsConstructor;

@Service
@RequiredArgsConstructor
public class OrgService {
    private final Logger logger = LoggerFactory.getLogger(OrgService.class);

    private final OrganizationRepository orgRepository;
    private final OrgRoleService orgRoleService;
    private final OrgMemberRepository memberRepository;
    private final OrgRoleRepository orgRoleRepository;
    private final UserRepository userRepository;
    private final OrgMapper orgMapper;
    private final SubscriptionService subscriptionService;

    public static enum OrgRoles {
        ROLE_ORG_ADMIN,
        ROLE_ORG_HR,
        ROLE_ORG_INTERVIEWER,
    }

    @Transactional
    public OrgDto.Response createOrganization(OrgDto.CreateRequest request,
            Authentication authentication) {
        User user = (User) authentication.getPrincipal();
        logger.info("User email: " + user.getEmail());

        SubscriptionStatus status = subscriptionService.getUserSubscriptionStatus(user.getId());
        if (status.hasReachedLimit()) {
            throw new SubscriptionLimitException(
                status.getCurrentOrganizationCount(),
                status.getMaxOrganizations(),
                status.getPlanName());
        }

        // Create organization
        Organization org = new Organization();
        org.setName(request.getName());
        org.setCreatedBy(user);

        // Save it
        org = orgRepository.save(org);
        logger.info("Created organization");

        // Create and add this user as a member
        OrgMember member = new OrgMember();
        member.setOrganization(org);
        member.setUser(user);
        memberRepository.save(member);
        logger.info("Created organization member");

        // Get it
        org = orgRepository.findById(org.getId()).orElseThrow();

        // Set the user role
        setUserRole(org, user, OrgRoles.ROLE_ORG_ADMIN);

        return orgMapper.toResponse(org);
    }

    public void setUserRole(Organization org, User user, OrgRoles role) {
        // Get the role
        OrganizationRole orgRole = orgRoleRepository.findByName(role.name());
        if (orgRole == null) {
            throw new IllegalArgumentException("Role not found");
        }
        // Map the user to the role
        orgRoleService.mapUserToOrgRole(org, user, orgRole);
        logger.info("User {} assigned role {} in organization {}", user.getEmail(), role.name(),
                org.getName());
    }

    @Transactional(readOnly = true)
    public OrgDto.DetailedResponse getOrganizationDetails(UUID orgId) {
        Organization org = orgRepository.findByIdWithMembersAndUsers(orgId)
                .orElseThrow(() -> new RuntimeException("Organization not found"));

        OrgDto.DetailedResponse response = new OrgDto.DetailedResponse();
        response.setId(org.getId());
        response.setName(org.getName());
        response.setCreatedBy(org.getCreatedBy().getEmail());

        response.setMembers(org.getMembers().stream().map(member -> {
            OrgDto.DetailedResponse.MemberDetails details = new OrgDto.DetailedResponse.MemberDetails();
            details.setUserId(member.getUser().getId());
            details.setEmail(member.getUser().getEmail());
            details.setRoles(orgRoleService.getUserRoles(org, member.getUser()).stream()
                    .map(OrganizationRole::getName).collect(Collectors.toSet()));
            return details;
        }).collect(Collectors.toSet()));
        return response;
    }

    @Transactional
    public OrgDto.DetailedResponse updateOrganization(UUID orgId, OrgDto.UpdateRequest request) {
        Organization org = orgRepository.findById(orgId)
                .orElseThrow(() -> new RuntimeException("Organization not found"));
        org.setName(request.getName());
        orgRepository.save(org);
        return orgMapper.toDetailedResponse(org);
    }

    @Transactional
    public ResponseDto<?> deleteOrganization(UUID orgId) {
        Organization org = orgRepository.findById(orgId)
                .orElseThrow(() -> new RuntimeException("Organization not found"));
        String orgName = org.getName();

        // Clear role mappings
        orgRoleService.deleteAllRoleMappingsForOrganization(org);
        memberRepository.deleteAllByOrganization(org);

        orgRepository.delete(org);
        logger.info("Organization {} deleted", orgName);
        return ResponseDto.success("Organization " + orgName + " deleted");
    }

    @Transactional
    public List<OrgDto.Response> getUserOrganizations(Authentication authentication) {
        User user = (User) authentication.getPrincipal();
        return orgRepository.findAllByMemberId(user.getId()).stream().map(orgMapper::toResponse)
                .collect(Collectors.toList());
    }

    @Transactional
    public List<OrgDto.Response> getAllOrganizations() {
        return orgRepository.findAll().stream().map(orgMapper::toResponse)
                .collect(Collectors.toList());
    }

    @Transactional
    public ResponseDto<?> addMember(UUID orgId, OrgDto.AddMemberRequest request) {
        Organization org = orgRepository.findById(orgId)
                .orElseThrow(() -> new RuntimeException("Organization not found"));
        User user = userRepository.findByEmail(request.getUserEmail())
                .orElseThrow(() -> new RuntimeException("User not found"));

        // Check if user is already a member
        if (memberRepository.existsByOrganizationAndUser(org, user)) {
            return ResponseDto.error(String.format("User %s is already a member of organization %s",
                    user.getEmail(), org.getName()), HttpStatus.BAD_REQUEST);
        }

        // Add member
        OrgMember member = new OrgMember();
        member.setOrganization(org);
        member.setUser(user);
        memberRepository.save(member);

        // Set role
        setUserRole(org, user, OrgRoles.valueOf(request.getRoleName()));

        return ResponseDto.success(String.format("User %s added to organization %s with role %s",
                user.getEmail(), org.getName(), request.getRoleName()));
    }
}
