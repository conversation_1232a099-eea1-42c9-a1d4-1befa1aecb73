package io.skillify.services.test;

import java.util.List;
import java.util.UUID;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import io.skillify.dtos.question.AnswerDto;
import io.skillify.dtos.test.TestDto;
import io.skillify.models.test.Test.TestStatus;

public interface TestService {

    TestDto.Response createTest(UUID jobId, TestDto.CreateRequest createRequest);

    TestDto.Response updateTest(UUID id, TestDto.UpdateRequest updateRequest);

    TestDto.Response getTestById(UUID id);

    List<TestDto.Response> getAllTests();

    Page<TestDto.Response> getTestsByOrganizationId(UUID orgId, Pageable pageable);

    List<TestDto.Response> getTestsByJobId(UUID jobId);

    List<TestDto.Response> getTestsByStatus(TestStatus status);

    List<TestDto.Response> getTestsByJobIdAndStatus(UUID jobId, TestStatus status);

    AnswerDto.TestAnswersResponse getTestResults(UUID testId, UUID candidateId, UUID questionId);

    void deleteTest(UUID id);

    long countTestsByJobId(UUID jobId);
}
