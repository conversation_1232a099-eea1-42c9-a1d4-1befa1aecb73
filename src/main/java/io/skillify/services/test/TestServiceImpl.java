package io.skillify.services.test;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import io.skillify.dtos.question.AnswerDto;
import io.skillify.dtos.test.TestDto;
import io.skillify.exceptions.ResourceNotFoundException;
import io.skillify.exceptions.TestLimitException;
import io.skillify.mappers.TestMapper;
import io.skillify.models.job.Job;
import io.skillify.models.question.Answer;
import io.skillify.models.test.Test;
import io.skillify.models.test.Test.TestStatus;
import io.skillify.models.test.TestAssignment;
import io.skillify.models.user.User;
import io.skillify.repositories.job.JobRepository;
import io.skillify.repositories.question.AnswerRepository;
import io.skillify.repositories.test.TestAssignmentRepository;
import io.skillify.repositories.test.TestRepository;
import io.skillify.repositories.user.UserRepository;
import io.skillify.services.subscription.SubscriptionService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Service
@RequiredArgsConstructor
@Transactional
@Slf4j
public class TestServiceImpl implements TestService {

    private final TestRepository testRepository;
    private final JobRepository jobRepository;
    private final TestMapper testMapper;
    private final AnswerRepository answerRepository;
    private final TestAssignmentRepository testAssignmentRepository;
    private final UserRepository userRepository;
    private final SubscriptionService subscriptionService;

    @Override
    public TestDto.Response createTest(UUID jobId, TestDto.CreateRequest createRequest) {
        Job job = jobRepository.findById(jobId)
                .orElseThrow(() -> new ResourceNotFoundException("Job not found with id: " + jobId));

        // Check test limits before creating
        UUID organizationId = job.getOrganization().getId();
        if (subscriptionService.hasReachedTestLimit(organizationId)) {
            long currentCount = subscriptionService.getCurrentTestCount(organizationId);
            int maxAllowed = subscriptionService.getOrganizationTestLimit(organizationId);
            String planName = subscriptionService.getOrganizationOwnerSubscription(organizationId).getPlan().getName();

            throw new TestLimitException(currentCount, maxAllowed, planName);
        }

        Test test = testMapper.toEntity(createRequest);
        test.setJob(job);
        test.setStatus(TestStatus.DRAFT); // New tests always start as drafts

        Test savedTest = testRepository.save(test);
        return testMapper.toDto(savedTest);
    }

    @Override
    public TestDto.Response updateTest(UUID id, TestDto.UpdateRequest updateRequest) {
        Test existingTest = testRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Test not found with id: " + id));

        // Update fields from DTO
        testMapper.updateEntityFromDto(updateRequest, existingTest);

        Test updatedTest = testRepository.save(existingTest);
        return testMapper.toDto(updatedTest);
    }

    @Override
    @Transactional(readOnly = true)
    public TestDto.Response getTestById(UUID id) {
        Test test = testRepository.findByIdWithJob(id)
                .orElseThrow(() -> new ResourceNotFoundException("Test not found with id: " + id));
        return testMapper.toDto(test);
    }

    @Override
    @Transactional(readOnly = true)
    public List<TestDto.Response> getAllTests() {
        return testRepository.findAll().stream()
                .map(testMapper::toDto)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(readOnly = true)
    public Page<TestDto.Response> getTestsByOrganizationId(UUID orgId, Pageable pageable) {
        Page<Test> testPage = testRepository.findByOrganizationId(orgId, pageable);
        return testPage.map(testMapper::toDto);
    }

    @Override
    @Transactional(readOnly = true)
    public List<TestDto.Response> getTestsByJobId(UUID jobId) {
        return testRepository.findByJobId(jobId).stream()
                .map(testMapper::toDto)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(readOnly = true)
    public List<TestDto.Response> getTestsByStatus(TestStatus status) {
        return testRepository.findByStatus(status).stream()
                .map(testMapper::toDto)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(readOnly = true)
    public List<TestDto.Response> getTestsByJobIdAndStatus(UUID jobId, TestStatus status) {
        return testRepository.findByJobIdAndStatus(jobId, status).stream()
                .map(testMapper::toDto)
                .collect(Collectors.toList());
    }

    @Override
    public void deleteTest(UUID id) {
        if (!testRepository.existsById(id)) {
            throw new ResourceNotFoundException("Test not found with id: " + id);
        }
        testRepository.deleteById(id);
    }

    @Override
    @Transactional(readOnly = true)
    public long countTestsByJobId(UUID jobId) {
        return testRepository.countByJobId(jobId);
    }

    @Override
    @Transactional(readOnly = true)
    public AnswerDto.TestAnswersResponse getTestResults(UUID testId, UUID candidateId, UUID questionId) {
        log.info("Fetching test results for test ID: {} with filters - candidateId: {}, questionId: {}",
                  testId, candidateId, questionId);

        AnswerDto.TestAnswersResponse response = new AnswerDto.TestAnswersResponse();
        response.setTestId(testId);
        List<AnswerDto.TestAnswersResponse.CandidateAnswers> candidateAnswersList = new ArrayList<>();

        if (candidateId != null) {
            // Specific candidate only
            candidateAnswersList.add(fetchSingleCandidateAnswers(testId, candidateId, questionId));
        } else {
            // Fetch all candidates
            List<TestAssignment> assignments = testAssignmentRepository.findByTestId(testId);

            for (TestAssignment assignment : assignments) {
                String candidateEmail = assignment.getCandidateEmail();
                Optional<User> candidateOpt = userRepository.findByEmail(candidateEmail);
                if (candidateOpt.isPresent()) {
                    candidateAnswersList.add(fetchSingleCandidateAnswers(testId, candidateOpt.get().getId(), questionId));
                } else {
                    log.warn("User not found for email {} while fetching test results for assignment {}", candidateEmail, assignment.getId());
                }
            }
        }

        // Filter out empty candidate answers
        candidateAnswersList = candidateAnswersList.stream()
                .filter(ca -> ca != null && ca.getAnswers() != null && !ca.getAnswers().isEmpty())
                .collect(Collectors.toList());

        response.setCandidateAnswers(candidateAnswersList);
        return response;
    }

    private AnswerDto.TestAnswersResponse.CandidateAnswers fetchSingleCandidateAnswers(UUID testId, UUID candidateId, UUID questionId) {
        List<Answer> answers;

        if (questionId != null) {
            // Filter by specific question
            Optional<Answer> answer = answerRepository.findByTestIdAndQuestionIdAndCandidateId(testId, questionId, candidateId);
            answers = answer.map(Collections::singletonList).orElse(Collections.emptyList());
        } else {
            // Get all answers for this candidate in this test
            answers = answerRepository.findByTestIdAndCandidateId(testId, candidateId);
        }

        if (answers.isEmpty()) {
            return null;
        }

        // Convert to DTOs
        List<AnswerDto.TestAnswersResponse.CandidateAnswers.AnswerCompact> answerCompactList = answers.stream()
                .map(answer -> AnswerDto.TestAnswersResponse.CandidateAnswers.AnswerCompact.builder()
                        .questionId(answer.getQuestionId())
                        .answer(answer.getAnswer())
                        .isCorrect(answer.getIsCorrect())
                        .score(answer.getScore())
                        .questionOrder(answer.getQuestion().getOrder())
                        .build())
                .collect(Collectors.toList());

        return AnswerDto.TestAnswersResponse.CandidateAnswers.builder()
                .candidateId(candidateId)
                .answers(answerCompactList)
                .build();
    }
}
