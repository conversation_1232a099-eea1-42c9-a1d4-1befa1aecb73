package io.skillify.repositories.org;

import org.springframework.data.jpa.repository.JpaRepository;

import io.skillify.models.org.OrgMember;
import io.skillify.models.org.Organization;
import io.skillify.models.user.User;

public interface OrgMemberRepository extends JpaRepository<OrgMember, OrgMember.OrgMemberId>  {
    long countByOrganization(Organization organization);

    void deleteAllByOrganization(Organization organization);

    boolean existsByOrganizationAndUser(Organization organization, User user);
}
