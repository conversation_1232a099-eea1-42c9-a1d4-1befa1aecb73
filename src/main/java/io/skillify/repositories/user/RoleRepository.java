package io.skillify.repositories.user;

import java.util.Optional;
import java.util.Set;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import io.skillify.models.user.Role;

@Repository
public interface RoleRepository extends JpaRepository<Role, Long> {
    Optional<Role> findByName(Role.RoleType name);

    // Find roles by their names
    Set<Role> findByNameIn(Set<Role.RoleType> names);
}
