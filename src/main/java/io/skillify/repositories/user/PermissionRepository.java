package io.skillify.repositories.user;

import io.skillify.models.user.Permission;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;
import java.util.Set;

@Repository
public interface PermissionRepository extends JpaRepository<Permission, Long> {

    Optional<Permission> findByPermissionName(String permissionName);

    Set<Permission> findByPermissionNameIn(Set<String> permissionNames);
} 