package io.skillify.repositories.subscription;

import java.time.OffsetDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import io.skillify.models.subscription.PaymentEvent;

@Repository
public interface PaymentEventRepository extends JpaRepository<PaymentEvent, UUID> {

    boolean existsByStripeEventId(String stripeEventId);

    Optional<PaymentEvent> findByStripeEventId(String stripeEventId);

    @Query("SELECT pe FROM PaymentEvent pe WHERE pe.processed = false AND pe.retryCount < 3 ORDER BY pe.createdAt ASC")
    List<PaymentEvent> findUnprocessedEventsForRetry();

    @Query("SELECT pe FROM PaymentEvent pe WHERE pe.createdAt < :cutoffTime AND pe.processed = true")
    List<PaymentEvent> findOldProcessedEvents(@Param("cutoffTime") OffsetDateTime cutoffTime);

    List<PaymentEvent> findByEventTypeAndProcessedFalse(String eventType);

    @Query("SELECT pe FROM PaymentEvent pe WHERE pe.subscription.id = :subscriptionId ORDER BY pe.createdAt DESC")
    List<PaymentEvent> findBySubscriptionIdOrderByCreatedAtDesc(@Param("subscriptionId") UUID subscriptionId);

    @Query("SELECT COUNT(pe) FROM PaymentEvent pe WHERE pe.eventType = :eventType AND pe.processed = true")
    long countProcessedEventsByType(@Param("eventType") String eventType);

    @Query("SELECT COUNT(pe) FROM PaymentEvent pe WHERE pe.processed = false")
    long countUnprocessedEvents();
}
