package io.skillify.repositories.subscription;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import io.skillify.models.subscription.SubscriptionPlan;

@Repository
public interface SubscriptionPlanRepository extends JpaRepository<SubscriptionPlan, UUID> {

    Optional<SubscriptionPlan> findByNameAndIsActive(String name, Boolean isActive);

    Optional<SubscriptionPlan> findByName(String name);

    List<SubscriptionPlan> findByIsActiveTrue();
}
