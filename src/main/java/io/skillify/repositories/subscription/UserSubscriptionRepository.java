package io.skillify.repositories.subscription;

import java.time.OffsetDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import io.skillify.models.subscription.UserSubscription;

@Repository
public interface UserSubscriptionRepository extends JpaRepository<UserSubscription, UUID> {

    @Query("SELECT us FROM UserSubscription us " +
            "JOIN FETCH us.plan " +
            "WHERE us.user.id = :userId AND us.status = :status")
    Optional<UserSubscription> findByUserIdAndStatus(@Param("userId") UUID userId, @Param("status") String status);

    @Query("SELECT us FROM UserSubscription us " +
            "WHERE us.status IN ('active', 'trial') " +
            "AND us.endedAt IS NOT NULL " +
            "AND us.endedAt < :currentTime")
    List<UserSubscription> findActiveSubscriptionsPassedEndDate(@Param("currentTime") OffsetDateTime currentTime);

    @Query("SELECT us FROM UserSubscription us " +
            "JOIN FETCH us.plan " +
            "WHERE us.user.id = :userId " +
            "ORDER BY us.createdAt DESC")
    List<UserSubscription> findAllByUserIdOrderByCreatedAtDesc(@Param("userId") UUID userId);

    boolean existsByUserIdAndStatus(UUID userId, String status);

    long countByUserId(UUID userId);

    @Query("SELECT us FROM UserSubscription us " +
            "JOIN FETCH us.plan " +
            "WHERE us.user.id = :userId " +
            "ORDER BY us.createdAt DESC " +
            "LIMIT 1")
    Optional<UserSubscription> findMostRecentByUserId(@Param("userId") UUID userId);

    @Query("SELECT COUNT(us) > 0 FROM UserSubscription us " +
            "JOIN us.plan p " +
            "WHERE us.user.id = :userId AND p.name = :planName AND us.status = :status")
    boolean existsByUserIdAndPlanNameAndStatus(@Param("userId") UUID userId,
            @Param("planName") String planName,
            @Param("status") String status);

    // Stripe-related queries
    @Query("SELECT us FROM UserSubscription us " +
            "JOIN FETCH us.plan " +
            "WHERE us.stripeSubscriptionId = :stripeSubscriptionId")
    Optional<UserSubscription> findByStripeSubscriptionId(@Param("stripeSubscriptionId") String stripeSubscriptionId);

    @Query("SELECT us FROM UserSubscription us " +
            "JOIN FETCH us.plan " +
            "WHERE us.stripeCustomerId = :stripeCustomerId")
    List<UserSubscription> findByStripeCustomerId(@Param("stripeCustomerId") String stripeCustomerId);

    @Query("SELECT us FROM UserSubscription us " +
            "JOIN FETCH us.plan " +
            "WHERE us.user.id = :userId AND us.status IN ('active', 'trial')")
    Optional<UserSubscription> findActiveByUserId(@Param("userId") UUID userId);

    @Query("SELECT us FROM UserSubscription us " +
            "WHERE us.stripeSubscriptionId IS NOT NULL " +
            "AND us.status = 'active' " +
            "AND us.currentPeriodEnd < :currentTime")
    List<UserSubscription> findExpiredStripeSubscriptions(@Param("currentTime") OffsetDateTime currentTime);

    @Query("SELECT us FROM UserSubscription us " +
            "WHERE us.status = 'trial' " +
            "AND us.trialEnd < :currentTime")
    List<UserSubscription> findExpiredTrialSubscriptions(@Param("currentTime") OffsetDateTime currentTime);

    long countByStatus(String status);

    @Query("SELECT COUNT(us) FROM UserSubscription us WHERE us.status = :status")
    long countByStatusQuery(@Param("status") String status);
}
