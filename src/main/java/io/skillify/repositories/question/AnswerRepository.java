package io.skillify.repositories.question;

import java.util.Collection;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import io.skillify.models.question.Answer;

@Repository
public interface AnswerRepository extends JpaRepository<Answer, Answer.AnswerId> {

    List<Answer> findByTestIdAndCandidateId(UUID testId, UUID candidateId);

    List<Answer> findByQuestionIdAndCandidateId(UUID questionId, UUID candidateId);

    Optional<Answer> findByTestIdAndQuestionIdAndCandidateId(UUID testId, UUID questionId, UUID candidateId);

    @Query("SELECT AVG(a.score) FROM Answer a WHERE a.test.id = :testId AND a.candidate.id = :candidateId AND a.score IS NOT NULL")
    Float calculateAverageScoreByTestAndCandidate(@Param("testId") UUID testId, @Param("candidateId") UUID candidateId);

    @Query("SELECT COUNT(a) FROM Answer a WHERE a.test.id = :testId AND a.candidate.id = :candidateId AND a.isCorrect = true")
    Long countCorrectAnswersByTestAndCandidate(@Param("testId") UUID testId, @Param("candidateId") UUID candidateId);

    @Query("SELECT COUNT(a) FROM Answer a WHERE a.test.id = :testId AND a.candidate.id = :candidateId")
    Long countTotalAnswersByTestAndCandidate(@Param("testId") UUID testId, @Param("candidateId") UUID candidateId);

    @Query("""
            SELECT a.questionId
            FROM Answer a
            WHERE a.testId = :testId
              AND a.candidateId = :candidateId
              AND a.questionId IN :questionIds
            """)
    Set<UUID> findQuestionIdsByTestIdAndCandidateIdAndQuestionIds(
            @Param("testId") UUID testId,
            @Param("candidateId") UUID candidateId,
            @Param("questionIds") Collection<UUID> questionIds);
}
