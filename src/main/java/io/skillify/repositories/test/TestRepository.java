package io.skillify.repositories.test;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import io.skillify.models.job.Job;
import io.skillify.models.test.Test;
import io.skillify.models.test.Test.TestStatus;

@Repository
public interface TestRepository extends JpaRepository<Test, UUID> {

    List<Test> findByJob(Job job);

    List<Test> findByJobId(UUID jobId);

    List<Test> findByStatus(TestStatus status);

    @Query("SELECT t FROM Test t WHERE t.job.id = :jobId AND t.status = :status")
    List<Test> findByJobIdAndStatus(@Param("jobId") UUID jobId, @Param("status") TestStatus status);

    @Query("SELECT t FROM Test t JOIN FETCH t.job WHERE t.id = :id")
    Optional<Test> findByIdWithJob(@Param("id") UUID id);

    @Query("SELECT COUNT(t) FROM Test t WHERE t.job.id = :jobId")
    long countByJobId(@Param("jobId") UUID jobId);

    @Query(value = """
                SELECT t FROM Test t
                JOIN t.job j
                    WHERE j.organization.id = :orgId
            """, countQuery = """
                SELECT count(t) FROM Test t
                JOIN t.job j
                    WHERE j.organization.id = :orgId
            """)
    Page<Test> findByOrganizationId(@Param("orgId") UUID orgId, Pageable pageable);
}
