package io.skillify.services.subscription;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.util.Optional;
import java.util.UUID;
import java.util.stream.Stream;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import io.skillify.constants.SubscriptionConstants;
import io.skillify.mappers.SubscriptionMapper;
import io.skillify.models.subscription.SubscriptionPlan;
import io.skillify.models.subscription.UserSubscription;
import io.skillify.models.user.User;
import io.skillify.repositories.org.OrganizationRepository;
import io.skillify.repositories.subscription.SubscriptionPlanRepository;
import io.skillify.repositories.subscription.UserSubscriptionRepository;
import io.skillify.repositories.test.TestRepository;
import io.skillify.repositories.user.UserRepository;

@ExtendWith(MockitoExtension.class)
@DisplayName("SubscriptionService Tests")
class SubscriptionServiceTest {

    @Mock
    private UserSubscriptionRepository subscriptionRepository;

    @Mock
    private SubscriptionPlanRepository planRepository;

    @Mock
    private OrganizationRepository organizationRepository;

    @Mock
    private TestRepository testRepository;

    @Mock
    private UserRepository userRepository;

    @Mock
    private SubscriptionMapper subscriptionMapper;

    @InjectMocks
    private SubscriptionService subscriptionService;

    private UUID userId;
    private User user;
    private SubscriptionPlan freePlan;
    private SubscriptionPlan proPlan;
    private UserSubscription freeSubscription;
    private UserSubscription proSubscription;

    @BeforeEach
    void setUp() {
        userId = UUID.randomUUID();
        user = User.builder().id(userId).build();
        setupSubscriptionPlans();
        setupUserSubscriptions();
    }

    private void setupSubscriptionPlans() {
        freePlan = SubscriptionPlan.builder()
                .id(UUID.randomUUID())
                .name(SubscriptionConstants.PlanNames.FREE)
                .displayName("Free Plan")
                .maxOrganizations(1)
                .isActive(true)
                .build();

        proPlan = SubscriptionPlan.builder()
                .id(UUID.randomUUID())
                .name(SubscriptionConstants.PlanNames.PRO)
                .displayName("Pro Plan")
                .maxOrganizations(5)
                .isActive(true)
                .build();
    }

    private void setupUserSubscriptions() {
        freeSubscription = UserSubscription.builder()
                .id(UUID.randomUUID())
                .plan(freePlan)
                .status(SubscriptionConstants.SubscriptionStatus.ACTIVE)
                .build();

        proSubscription = UserSubscription.builder()
                .id(UUID.randomUUID())
                .plan(proPlan)
                .status(SubscriptionConstants.SubscriptionStatus.ACTIVE)
                .build();
    }

    @Nested
    @DisplayName("Organization Limit Validation")
    class OrganizationLimitValidationTests {

        @Test
        @DisplayName("Should allow organization creation when under limit")
        void shouldAllowOrganizationCreation_WhenUnderLimit() {
            // Given
            when(subscriptionRepository.findByUserIdAndStatus(userId, SubscriptionConstants.SubscriptionStatus.ACTIVE))
                    .thenReturn(Optional.of(freeSubscription));
            when(organizationRepository.countByCreatedById(userId))
                    .thenReturn(0L);

            // When
            boolean hasReachedLimit = subscriptionService.hasReachedOrganizationLimit(userId);

            // Then
            assertFalse(hasReachedLimit, "User should be able to create organization when under limit");
        }

        @Test
        @DisplayName("Should prevent organization creation when at limit")
        void shouldPreventOrganizationCreation_WhenAtLimit() {
            // Given
            when(subscriptionRepository.findByUserIdAndStatus(userId, SubscriptionConstants.SubscriptionStatus.ACTIVE))
                    .thenReturn(Optional.of(freeSubscription));
            when(organizationRepository.countByCreatedById(userId))
                    .thenReturn(1L);

            // When
            boolean hasReachedLimit = subscriptionService.hasReachedOrganizationLimit(userId);

            // Then
            assertTrue(hasReachedLimit, "User should not be able to create organization when at limit");
        }

        @ParameterizedTest(name = "Free plan: {0} organizations created -> limit reached: {1}")
        @MethodSource("freePlanLimitTestData")
        @DisplayName("Should validate free plan organization limits correctly")
        void shouldValidateFreePlanLimits(long organizationsCreated, boolean expectedLimitReached) {
            // Given
            when(subscriptionRepository.findByUserIdAndStatus(userId, SubscriptionConstants.SubscriptionStatus.ACTIVE))
                    .thenReturn(Optional.of(freeSubscription));
            when(organizationRepository.countByCreatedById(userId))
                    .thenReturn(organizationsCreated);

            // When
            boolean hasReachedLimit = subscriptionService.hasReachedOrganizationLimit(userId);

            // Then
            assertEquals(expectedLimitReached, hasReachedLimit);
        }

        @ParameterizedTest(name = "Pro plan: {0} organizations created -> limit reached: {1}")
        @MethodSource("proPlanLimitTestData")
        @DisplayName("Should validate pro plan organization limits correctly")
        void shouldValidateProPlanLimits(long organizationsCreated, boolean expectedLimitReached) {
            // Given
            when(subscriptionRepository.findByUserIdAndStatus(userId, SubscriptionConstants.SubscriptionStatus.ACTIVE))
                    .thenReturn(Optional.of(proSubscription));
            when(organizationRepository.countByCreatedById(userId))
                    .thenReturn(organizationsCreated);

            // When
            boolean hasReachedLimit = subscriptionService.hasReachedOrganizationLimit(userId);

            // Then
            assertEquals(expectedLimitReached, hasReachedLimit);
        }

        @Test
        @DisplayName("Should create default subscription when user has no active subscription")
        void shouldCreateDefaultSubscription_WhenNoActiveSubscription() {
            // Given
            when(subscriptionRepository.findByUserIdAndStatus(userId, SubscriptionConstants.SubscriptionStatus.ACTIVE))
                    .thenReturn(Optional.empty());
            when(planRepository.findByName(SubscriptionConstants.PlanNames.FREE))
                    .thenReturn(Optional.of(freePlan));
            when(userRepository.getReferenceById(userId))
                    .thenReturn(user);
            when(subscriptionRepository.save(any(UserSubscription.class)))
                    .thenReturn(freeSubscription);

            // When
            boolean hasReachedLimit = subscriptionService.hasReachedOrganizationLimit(userId);

            // Then
            verify(subscriptionRepository).save(any(UserSubscription.class));
            assertFalse(hasReachedLimit, "New user with default free plan should not have reached limit");
        }

        static Stream<Arguments> freePlanLimitTestData() {
            return Stream.of(
                    Arguments.of(0L, false),
                    Arguments.of(1L, true),
                    Arguments.of(2L, true));
        }

        static Stream<Arguments> proPlanLimitTestData() {
            return Stream.of(
                    Arguments.of(0L, false),
                    Arguments.of(1L, false),
                    Arguments.of(4L, false),
                    Arguments.of(5L, true),
                    Arguments.of(6L, true));
        }
    }

    @Nested
    @DisplayName("User Subscription Management")
    class UserSubscriptionManagementTests {

        @Test
        @DisplayName("Should return existing active subscription when available")
        void shouldReturnExistingSubscription_WhenActive() {
            // Given
            when(subscriptionRepository.findByUserIdAndStatus(userId, SubscriptionConstants.SubscriptionStatus.ACTIVE))
                    .thenReturn(Optional.of(freeSubscription));

            // When
            UserSubscription result = subscriptionService.getUserActiveSubscription(userId);

            // Then
            assertNotNull(result, "Active subscription should not be null");
            assertEquals(freeSubscription.getId(), result.getId());
            assertEquals(freePlan, result.getPlan());
            assertEquals(SubscriptionConstants.SubscriptionStatus.ACTIVE, result.getStatus());
            verify(subscriptionRepository, never()).save(any(UserSubscription.class));
        }

        @Test
        @DisplayName("Should create and return default subscription when no active subscription exists")
        void shouldCreateDefaultSubscription_WhenNoActiveSubscriptionExists() {
            // Given
            when(subscriptionRepository.findByUserIdAndStatus(userId, SubscriptionConstants.SubscriptionStatus.ACTIVE))
                    .thenReturn(Optional.empty());
            when(planRepository.findByName(SubscriptionConstants.PlanNames.FREE))
                    .thenReturn(Optional.of(freePlan));
            when(userRepository.getReferenceById(userId))
                    .thenReturn(user);
            when(subscriptionRepository.save(any(UserSubscription.class)))
                    .thenReturn(freeSubscription);

            // When
            UserSubscription result = subscriptionService.getUserActiveSubscription(userId);

            // Then
            assertNotNull(result, "Created subscription should not be null");
            assertEquals(freePlan, result.getPlan());
            assertEquals(SubscriptionConstants.SubscriptionStatus.ACTIVE, result.getStatus());

            ArgumentCaptor<UserSubscription> subscriptionCaptor = ArgumentCaptor.forClass(UserSubscription.class);
            verify(subscriptionRepository).save(subscriptionCaptor.capture());

            UserSubscription savedSubscription = subscriptionCaptor.getValue();
            assertNotNull(savedSubscription.getUser(), "User should be set");
            assertEquals(freePlan, savedSubscription.getPlan());
            assertEquals(SubscriptionConstants.SubscriptionStatus.ACTIVE, savedSubscription.getStatus());
        }

        @Test
        @DisplayName("Should create default free subscription with correct properties")
        void shouldCreateDefaultFreeSubscription_WithCorrectProperties() {
            // Given
            when(planRepository.findByName(SubscriptionConstants.PlanNames.FREE))
                    .thenReturn(Optional.of(freePlan));
            when(userRepository.getReferenceById(userId))
                    .thenReturn(user);
            when(subscriptionRepository.save(any(UserSubscription.class)))
                    .thenReturn(freeSubscription);

            // When
            UserSubscription result = subscriptionService.createDefaultFreeSubscription(userId);

            // Then
            assertNotNull(result, "Created subscription should not be null");
            assertEquals(freePlan, result.getPlan());
            assertEquals(SubscriptionConstants.SubscriptionStatus.ACTIVE, result.getStatus());

            ArgumentCaptor<UserSubscription> subscriptionCaptor = ArgumentCaptor.forClass(UserSubscription.class);
            verify(subscriptionRepository).save(subscriptionCaptor.capture());

            UserSubscription savedSubscription = subscriptionCaptor.getValue();
            assertNotNull(savedSubscription.getUser(), "User should be set");
            assertEquals(freePlan, savedSubscription.getPlan());
            assertEquals(SubscriptionConstants.SubscriptionStatus.ACTIVE, savedSubscription.getStatus());
        }

        @Test
        @DisplayName("Should throw exception when free plan not found during default subscription creation")
        void shouldThrowException_WhenFreePlanNotFoundDuringDefaultCreation() {
            // Given
            when(planRepository.findByName(SubscriptionConstants.PlanNames.FREE))
                    .thenReturn(Optional.empty());

            // When & Then
            Exception exception = assertThrows(
                    Exception.class,
                    () -> subscriptionService.createDefaultFreeSubscription(userId),
                    "Should throw exception when free plan not found");

            assertTrue(exception.getMessage().contains("plan not found"));
            verify(subscriptionRepository, never()).save(any(UserSubscription.class));
        }
    }

    @Nested
    @DisplayName("Organization Count and Limits")
    class OrganizationCountAndLimitsTests {

        @ParameterizedTest(name = "Should return {0} organizations for user")
        @ValueSource(longs = { 0L, 1L, 2L, 5L, 10L })
        @DisplayName("Should return correct organization count")
        void shouldReturnCorrectOrganizationCount(long expectedCount) {
            // Given
            when(organizationRepository.countByCreatedById(userId))
                    .thenReturn(expectedCount);

            // When
            long actualCount = subscriptionService.getCurrentOrganizationCount(userId);

            // Then
            assertEquals(expectedCount, actualCount,
                    "Current organization count should match expected count");
        }

        @Test
        @DisplayName("Should return correct max organizations for free plan")
        void shouldReturnCorrectMaxOrganizations_ForFreePlan() {
            // Given
            when(subscriptionRepository.findByUserIdAndStatus(userId, SubscriptionConstants.SubscriptionStatus.ACTIVE))
                    .thenReturn(Optional.of(freeSubscription));

            // When
            int maxOrganizations = subscriptionService.getMaxOrganizations(userId);

            // Then
            assertEquals(1, maxOrganizations, "Free plan should allow 1 organization");
        }

        @Test
        @DisplayName("Should return correct max organizations for pro plan")
        void shouldReturnCorrectMaxOrganizations_ForProPlan() {
            // Given
            when(subscriptionRepository.findByUserIdAndStatus(userId, SubscriptionConstants.SubscriptionStatus.ACTIVE))
                    .thenReturn(Optional.of(proSubscription));

            // When
            int maxOrganizations = subscriptionService.getMaxOrganizations(userId);

            // Then
            assertEquals(5, maxOrganizations, "Pro plan should allow 5 organizations");
        }

        @Test
        @DisplayName("Should return default max organizations when no subscription found")
        void shouldReturnDefaultMaxOrganizations_WhenNoSubscriptionFound() {
            // Given
            when(subscriptionRepository.findByUserIdAndStatus(userId, SubscriptionConstants.SubscriptionStatus.ACTIVE))
                    .thenReturn(Optional.empty());
            when(planRepository.findByName(SubscriptionConstants.PlanNames.FREE))
                    .thenReturn(Optional.of(freePlan));
            when(userRepository.getReferenceById(userId))
                    .thenReturn(user);
            when(subscriptionRepository.save(any(UserSubscription.class)))
                    .thenReturn(freeSubscription);

            // When
            int maxOrganizations = subscriptionService.getMaxOrganizations(userId);

            // Then
            assertEquals(1, maxOrganizations,
                    "Should return default free plan limit when no subscription found");
        }
    }

    @Nested
    @DisplayName("Error Scenarios")
    class ErrorScenariosTests {

        @Test
        @DisplayName("Should handle null user ID gracefully")
        void shouldHandleNullUserIdGracefully() {
            // Given
            when(subscriptionRepository.findByUserIdAndStatus(null, SubscriptionConstants.SubscriptionStatus.ACTIVE))
                    .thenReturn(Optional.empty());
            when(planRepository.findByName(SubscriptionConstants.PlanNames.FREE))
                    .thenReturn(Optional.empty()); // This will cause Exception

            // When & Then
            assertThrows(Exception.class,
                    () -> subscriptionService.hasReachedOrganizationLimit(null),
                    "Should throw exception when free plan not found");
        }

        @Test
        @DisplayName("Should handle repository exceptions gracefully")
        void shouldHandleRepositoryExceptionsGracefully() {
            // Given
            when(subscriptionRepository.findByUserIdAndStatus(userId, SubscriptionConstants.SubscriptionStatus.ACTIVE))
                    .thenThrow(new RuntimeException("Database connection failed"));

            // When & Then
            assertThrows(RuntimeException.class,
                    () -> subscriptionService.hasReachedOrganizationLimit(userId),
                    "Should propagate repository exceptions");
        }

        @Test
        @DisplayName("Should handle missing subscription plan gracefully")
        void shouldHandleMissingSubscriptionPlanGracefully() {
            // Given
            UserSubscription subscriptionWithoutPlan = UserSubscription.builder()
                    .id(UUID.randomUUID())
                    .plan(null)
                    .status(SubscriptionConstants.SubscriptionStatus.ACTIVE)
                    .build();

            when(subscriptionRepository.findByUserIdAndStatus(userId, SubscriptionConstants.SubscriptionStatus.ACTIVE))
                    .thenReturn(Optional.of(subscriptionWithoutPlan));

            // When & Then
            assertThrows(NullPointerException.class,
                    () -> subscriptionService.hasReachedOrganizationLimit(userId),
                    "Should throw NullPointerException when subscription plan is null");
        }
    }

    @Nested
    @DisplayName("Integration Scenarios")
    class IntegrationScenariosTests {

        @Test
        @DisplayName("Should handle complete user subscription lifecycle")
        void shouldHandleCompleteUserSubscriptionLifecycle() {
            // Given - New user with no subscription
            when(subscriptionRepository.findByUserIdAndStatus(userId, SubscriptionConstants.SubscriptionStatus.ACTIVE))
                    .thenReturn(Optional.empty());
            when(planRepository.findByName(SubscriptionConstants.PlanNames.FREE))
                    .thenReturn(Optional.of(freePlan));
            when(userRepository.getReferenceById(userId))
                    .thenReturn(user);
            when(subscriptionRepository.save(any(UserSubscription.class)))
                    .thenReturn(freeSubscription);
            when(organizationRepository.countByCreatedById(userId))
                    .thenReturn(0L);

            // When - Check limit for new user (should create subscription)
            boolean firstCheck = subscriptionService.hasReachedOrganizationLimit(userId);

            // Then - Should create subscription and not reach limit
            assertFalse(firstCheck, "New user should not have reached limit");
            verify(subscriptionRepository).save(any(UserSubscription.class));

            // When - Get count and max organizations for the created subscription
            long currentCount = subscriptionService.getCurrentOrganizationCount(userId);
            int maxOrganizations = subscriptionService.getMaxOrganizations(userId);

            // Then - Should reflect the free plan limits
            assertEquals(0L, currentCount, "Current organization count should be 0");
            assertEquals(1, maxOrganizations, "Free plan should allow 1 organization");
        }

        @Test
        @DisplayName("Should handle edge case where user has max organizations")
        void shouldHandleEdgeCaseWhereUserHasMaxOrganizations() {
            // Given - User with pro plan at exactly the limit
            when(subscriptionRepository.findByUserIdAndStatus(userId, SubscriptionConstants.SubscriptionStatus.ACTIVE))
                    .thenReturn(Optional.of(proSubscription));
            when(organizationRepository.countByCreatedById(userId))
                    .thenReturn(5L); // Pro plan allows 5 organizations

            // When
            boolean hasReachedLimit = subscriptionService.hasReachedOrganizationLimit(userId);
            int maxOrganizations = subscriptionService.getMaxOrganizations(userId);
            long currentCount = subscriptionService.getCurrentOrganizationCount(userId);

            // Then
            assertTrue(hasReachedLimit, "User should have reached limit");
            assertEquals(5, maxOrganizations, "Pro plan should allow 5 organizations");
            assertEquals(5L, currentCount, "Current count should be 5");
        }
    }

    @Nested
    @DisplayName("Cache Eviction Tests")
    class CacheEvictionTests {

        @Test
        @DisplayName("Should update user subscription and evict cache")
        void shouldUpdateUserSubscriptionAndEvictCache() {
            // Given
            UserSubscription existingSubscription = UserSubscription.builder()
                    .id(UUID.randomUUID())
                    .plan(freePlan)
                    .status(SubscriptionConstants.SubscriptionStatus.ACTIVE)
                    .build();

            when(subscriptionRepository.findByUserIdAndStatus(userId, SubscriptionConstants.SubscriptionStatus.ACTIVE))
                    .thenReturn(Optional.of(existingSubscription));
            when(planRepository.findByName(SubscriptionConstants.PlanNames.PRO))
                    .thenReturn(Optional.of(proPlan));
            when(subscriptionRepository.save(any(UserSubscription.class)))
                    .thenReturn(existingSubscription);

            // When
            UserSubscription result = subscriptionService.updateUserSubscription(userId,
                    SubscriptionConstants.PlanNames.PRO);

            // Then
            assertNotNull(result, "Updated subscription should not be null");
            verify(subscriptionRepository).save(existingSubscription);
            assertEquals(proPlan, existingSubscription.getPlan());
        }



        @Test
        @DisplayName("Should throw exception when updating non-existent subscription")
        void shouldThrowExceptionWhenUpdatingNonExistentSubscription() {
            // Given
            when(subscriptionRepository.findByUserIdAndStatus(userId, SubscriptionConstants.SubscriptionStatus.ACTIVE))
                    .thenReturn(Optional.empty());

            // When & Then
            Exception exception = assertThrows(
                    Exception.class,
                    () -> subscriptionService.updateUserSubscription(userId, SubscriptionConstants.PlanNames.PRO),
                    "Should throw exception when no active subscription found");

            assertTrue(exception.getMessage().contains("No active subscription found"));
        }

        @Test
        @DisplayName("Should cancel user subscription and evict cache")
        void shouldCancelUserSubscriptionAndEvictCache() {
            // Given
            UserSubscription activeSubscription = UserSubscription.builder()
                    .id(UUID.randomUUID())
                    .plan(freePlan)
                    .status(SubscriptionConstants.SubscriptionStatus.ACTIVE)
                    .build();

            when(subscriptionRepository.findByUserIdAndStatus(userId, SubscriptionConstants.SubscriptionStatus.ACTIVE))
                    .thenReturn(Optional.of(activeSubscription));
            when(subscriptionRepository.save(any(UserSubscription.class)))
                    .thenReturn(activeSubscription);

            // When
            subscriptionService.cancelUserSubscription(userId, false);

            // Then
            verify(subscriptionRepository).save(activeSubscription);
            assertEquals(SubscriptionConstants.SubscriptionStatus.CANCELLED, activeSubscription.getStatus());
        }
    }

    @Nested
    @DisplayName("Test Limit Methods")
    class TestLimitMethods {

        private UUID organizationId;
        private io.skillify.models.org.Organization organization;

        @BeforeEach
        void setUp() {
            organizationId = UUID.randomUUID();
            organization = io.skillify.models.org.Organization.builder()
                    .id(organizationId)
                    .createdBy(user)
                    .build();
        }

        @Test
        @DisplayName("Should return current test count for organization")
        void shouldReturnCurrentTestCount() {
            // Given
            long expectedCount = 5L;
            when(testRepository.countByOrganizationId(organizationId)).thenReturn(expectedCount);

            // When
            long actualCount = subscriptionService.getCurrentTestCount(organizationId);

            // Then
            assertEquals(expectedCount, actualCount);
            verify(testRepository).countByOrganizationId(organizationId);
        }

        @Test
        @DisplayName("Should return organization test limit")
        void shouldReturnOrganizationTestLimit() {
            // Given
            when(organizationRepository.findById(organizationId)).thenReturn(Optional.of(organization));
            when(subscriptionRepository.findByUserIdAndStatus(userId, SubscriptionConstants.SubscriptionStatus.ACTIVE))
                    .thenReturn(Optional.of(proSubscription));

            // When
            int testLimit = subscriptionService.getOrganizationTestLimit(organizationId);

            // Then
            assertEquals(15, testLimit); // Pro plan has 15 tests
            verify(organizationRepository).findById(organizationId);
        }

        @Test
        @DisplayName("Should return false when test limit not reached")
        void shouldReturnFalseWhenTestLimitNotReached() {
            // Given
            when(organizationRepository.findById(organizationId)).thenReturn(Optional.of(organization));
            when(subscriptionRepository.findByUserIdAndStatus(userId, SubscriptionConstants.SubscriptionStatus.ACTIVE))
                    .thenReturn(Optional.of(proSubscription));
            when(testRepository.countByOrganizationId(organizationId)).thenReturn(10L); // 10 < 15

            // When
            boolean hasReached = subscriptionService.hasReachedTestLimit(organizationId);

            // Then
            assertFalse(hasReached);
        }

        @Test
        @DisplayName("Should return true when test limit reached")
        void shouldReturnTrueWhenTestLimitReached() {
            // Given
            when(organizationRepository.findById(organizationId)).thenReturn(Optional.of(organization));
            when(subscriptionRepository.findByUserIdAndStatus(userId, SubscriptionConstants.SubscriptionStatus.ACTIVE))
                    .thenReturn(Optional.of(proSubscription));
            when(testRepository.countByOrganizationId(organizationId)).thenReturn(15L); // 15 >= 15

            // When
            boolean hasReached = subscriptionService.hasReachedTestLimit(organizationId);

            // Then
            assertTrue(hasReached);
        }

        @Test
        @DisplayName("Should return true when test limit exceeded")
        void shouldReturnTrueWhenTestLimitExceeded() {
            // Given
            when(organizationRepository.findById(organizationId)).thenReturn(Optional.of(organization));
            when(subscriptionRepository.findByUserIdAndStatus(userId, SubscriptionConstants.SubscriptionStatus.ACTIVE))
                    .thenReturn(Optional.of(freeSubscription));
            when(testRepository.countByOrganizationId(organizationId)).thenReturn(5L); // 5 > 2

            // When
            boolean hasReached = subscriptionService.hasReachedTestLimit(organizationId);

            // Then
            assertTrue(hasReached);
        }

        @Test
        @DisplayName("Should return organization owner subscription")
        void shouldReturnOrganizationOwnerSubscription() {
            // Given
            when(organizationRepository.findById(organizationId)).thenReturn(Optional.of(organization));
            when(subscriptionRepository.findByUserIdAndStatus(userId, SubscriptionConstants.SubscriptionStatus.ACTIVE))
                    .thenReturn(Optional.of(proSubscription));

            // When
            UserSubscription ownerSubscription = subscriptionService.getOrganizationOwnerSubscription(organizationId);

            // Then
            assertEquals(proSubscription, ownerSubscription);
            verify(organizationRepository).findById(organizationId);
        }
    }

}
