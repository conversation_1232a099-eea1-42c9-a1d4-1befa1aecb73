package io.skillify.services.test;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.util.Optional;
import java.util.UUID;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import io.skillify.dtos.test.TestDto;
import io.skillify.exceptions.ResourceNotFoundException;
import io.skillify.exceptions.TestLimitException;
import io.skillify.mappers.TestMapper;
import io.skillify.models.job.Job;
import io.skillify.models.org.Organization;
import io.skillify.models.subscription.SubscriptionPlan;
import io.skillify.models.subscription.UserSubscription;
import io.skillify.models.test.Test;
import io.skillify.models.test.Test.TestStatus;
import io.skillify.models.user.User;
import io.skillify.repositories.job.JobRepository;
import io.skillify.repositories.question.AnswerRepository;
import io.skillify.repositories.test.TestAssignmentRepository;
import io.skillify.repositories.test.TestRepository;
import io.skillify.repositories.user.UserRepository;
import io.skillify.services.subscription.SubscriptionService;

@ExtendWith(MockitoExtension.class)
@DisplayName("TestServiceImpl Tests")
class TestServiceImplTest {

    @Mock
    private TestRepository testRepository;

    @Mock
    private JobRepository jobRepository;

    @Mock
    private TestMapper testMapper;

    @Mock
    private AnswerRepository answerRepository;

    @Mock
    private TestAssignmentRepository testAssignmentRepository;

    @Mock
    private UserRepository userRepository;

    @Mock
    private SubscriptionService subscriptionService;

    @InjectMocks
    private TestServiceImpl testService;

    private UUID jobId;
    private UUID organizationId;
    private UUID userId;
    private Job job;
    private Organization organization;
    private User user;
    private Test test;
    private TestDto.CreateRequest createRequest;
    private TestDto.Response testResponse;
    private SubscriptionPlan freePlan;
    private SubscriptionPlan proPlan;
    private UserSubscription freeSubscription;

    @BeforeEach
    void setUp() {
        jobId = UUID.randomUUID();
        organizationId = UUID.randomUUID();
        userId = UUID.randomUUID();

        user = User.builder()
                .id(userId)
                .build();

        organization = Organization.builder()
                .id(organizationId)
                .createdBy(user)
                .build();

        job = Job.builder()
                .id(jobId)
                .organization(organization)
                .build();

        test = Test.builder()
                .id(UUID.randomUUID())
                .job(job)
                .status(TestStatus.DRAFT)
                .build();

        createRequest = TestDto.CreateRequest.builder()
                .title("Test Title")
                .description("Test Description")
                .build();

        testResponse = TestDto.Response.builder()
                .id(test.getId())
                .title("Test Title")
                .description("Test Description")
                .status(TestStatus.DRAFT)
                .build();

        freePlan = SubscriptionPlan.builder()
                .id(UUID.randomUUID())
                .name("Free")
                .maxTests(2)
                .build();

        proPlan = SubscriptionPlan.builder()
                .id(UUID.randomUUID())
                .name("Pro")
                .maxTests(15)
                .build();

        freeSubscription = UserSubscription.builder()
                .id(UUID.randomUUID())
                .plan(freePlan)
                .build();
    }

    @Nested
    @DisplayName("Create Test")
    class CreateTest {

        @Test
        @DisplayName("Should create test successfully when under limit")
        void shouldCreateTestSuccessfullyWhenUnderLimit() {
            // Given
            when(jobRepository.findById(jobId)).thenReturn(Optional.of(job));
            when(subscriptionService.hasReachedTestLimit(organizationId)).thenReturn(false);
            when(testMapper.toEntity(createRequest)).thenReturn(test);
            when(testRepository.save(test)).thenReturn(test);
            when(testMapper.toDto(test)).thenReturn(testResponse);

            // When
            TestDto.Response result = testService.createTest(jobId, createRequest);

            // Then
            assertNotNull(result);
            assertEquals(testResponse.getId(), result.getId());
            assertEquals(TestStatus.DRAFT, result.getStatus());
            verify(jobRepository).findById(jobId);
            verify(subscriptionService).hasReachedTestLimit(organizationId);
            verify(testRepository).save(test);
        }

        @Test
        @DisplayName("Should throw TestLimitException when test limit reached")
        void shouldThrowTestLimitExceptionWhenTestLimitReached() {
            // Given
            when(jobRepository.findById(jobId)).thenReturn(Optional.of(job));
            when(subscriptionService.hasReachedTestLimit(organizationId)).thenReturn(true);
            when(subscriptionService.getCurrentTestCount(organizationId)).thenReturn(2L);
            when(subscriptionService.getOrganizationTestLimit(organizationId)).thenReturn(2);
            when(subscriptionService.getOrganizationOwnerSubscription(organizationId)).thenReturn(freeSubscription);

            // When & Then
            TestLimitException exception = assertThrows(TestLimitException.class, 
                    () -> testService.createTest(jobId, createRequest));

            assertEquals(2L, exception.getCurrentCount());
            assertEquals(2, exception.getMaxAllowed());
            assertEquals("Free", exception.getPlanName());
            verify(testRepository, never()).save(any(Test.class));
        }

        @Test
        @DisplayName("Should throw ResourceNotFoundException when job not found")
        void shouldThrowResourceNotFoundExceptionWhenJobNotFound() {
            // Given
            when(jobRepository.findById(jobId)).thenReturn(Optional.empty());

            // When & Then
            assertThrows(ResourceNotFoundException.class, 
                    () -> testService.createTest(jobId, createRequest));

            verify(subscriptionService, never()).hasReachedTestLimit(any(UUID.class));
            verify(testRepository, never()).save(any(Test.class));
        }
    }
}
