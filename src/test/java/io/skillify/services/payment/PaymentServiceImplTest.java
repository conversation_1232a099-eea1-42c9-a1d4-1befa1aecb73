package io.skillify.services.payment;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.time.OffsetDateTime;
import java.util.Optional;
import java.util.UUID;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.dao.DataIntegrityViolationException;

import io.skillify.constants.SubscriptionConstants;
import io.skillify.exceptions.BadRequestException;
import io.skillify.exceptions.ResourceNotFoundException;
import io.skillify.models.subscription.SubscriptionPlan;
import io.skillify.models.subscription.UserSubscription;
import io.skillify.models.user.User;
import io.skillify.repositories.subscription.SubscriptionPlanRepository;
import io.skillify.repositories.subscription.UserSubscriptionRepository;
import io.skillify.services.payment.PaymentService.CancelSubscriptionData;

@ExtendWith(MockitoExtension.class)
@DisplayName("PaymentServiceImpl Tests")
class PaymentServiceImplTest {

    @Mock
    private StripeService stripeService;

    @Mock
    private SubscriptionPlanRepository planRepository;

    @Mock
    private UserSubscriptionRepository subscriptionRepository;

    @InjectMocks
    private PaymentServiceImpl paymentService;

    private UUID userId;
    private User user;
    private SubscriptionPlan freePlan;
    private SubscriptionPlan proPlan;
    private UserSubscription proSubscription;
    private UserSubscription freeSubscription;
    private com.stripe.model.Subscription mockStripeSubscription;

    @BeforeEach
    void setUp() {
        userId = UUID.randomUUID();
        user = User.builder()
                .id(userId)
                .username("testuser")
                .email("<EMAIL>")
                .build();

        setupSubscriptionPlans();
        setupUserSubscriptions();
        setupMockStripeSubscription();
    }

    private void setupSubscriptionPlans() {
        freePlan = SubscriptionPlan.builder()
                .id(UUID.randomUUID())
                .name(SubscriptionConstants.PlanNames.FREE)
                .displayName("Free Plan")
                .maxOrganizations(1)
                .priceMonthlyCents(0)
                .isActive(true)
                .build();

        proPlan = SubscriptionPlan.builder()
                .id(UUID.randomUUID())
                .name(SubscriptionConstants.PlanNames.PRO)
                .displayName("Pro Plan")
                .maxOrganizations(5)
                .priceMonthlyCents(999)
                .stripePriceId("price_123")
                .stripeProductId("prod_123")
                .isActive(true)
                .build();
    }

    private void setupUserSubscriptions() {
        OffsetDateTime now = OffsetDateTime.now();

        proSubscription = UserSubscription.builder()
                .id(UUID.randomUUID())
                .user(user)
                .plan(proPlan)
                .status(SubscriptionConstants.SubscriptionStatus.ACTIVE)
                .stripeSubscriptionId("sub_123456789")
                .stripeCustomerId("cus_123456789")
                .startedAt(now.minusMonths(1))
                .currentPeriodStart(now.minusMonths(1))
                .currentPeriodEnd(now.plusDays(7)) // 7 days remaining
                .createdAt(now.minusMonths(1))
                .updatedAt(now)
                .build();

        freeSubscription = UserSubscription.builder()
                .id(UUID.randomUUID())
                .user(user)
                .plan(freePlan)
                .status(SubscriptionConstants.SubscriptionStatus.ACTIVE)
                .startedAt(now)
                .createdAt(now)
                .updatedAt(now)
                .build();
    }

    private void setupMockStripeSubscription() {
        mockStripeSubscription = new com.stripe.model.Subscription();
        mockStripeSubscription.setId("sub_mock_123");
    }

    @Nested
    @DisplayName("Subscription Cancellation Tests")
    class o {

        @Nested
        @DisplayName("Immediate Cancellation")
        class ImmediateCancellationTests {

            @Test
            @DisplayName("Should immediately cancel Pro subscription and create free subscription")
            void shouldImmediatelyCancelProSubscription_AndCreateFreeSubscription() {
                // Given
                boolean immediate = true;
                String reason = "No longer needed";

                when(subscriptionRepository.findActiveByUserId(userId))
                        .thenReturn(Optional.of(proSubscription))
                        .thenReturn(Optional.empty()); // No active after cancellation
                when(stripeService.cancelSubscription(anyString(), anyBoolean())).thenReturn(mockStripeSubscription);
                when(subscriptionRepository.saveAndFlush(any(UserSubscription.class)))
                        .thenReturn(proSubscription);
                when(planRepository.findByName(SubscriptionConstants.PlanNames.FREE))
                        .thenReturn(Optional.of(freePlan));
                when(subscriptionRepository.save(any(UserSubscription.class)))
                        .thenReturn(freeSubscription);

                // When
                CancelSubscriptionData result = paymentService.cancelSubscription(user, immediate, reason);

                // Then
                assertNotNull(result);
                assertNotNull(result.cancelledSubscription());
                assertNotNull(result.freePlanSubscription());

                // Verify Stripe cancellation
                verify(stripeService).cancelSubscription("sub_123456789", true);

                // Verify subscription status update
                ArgumentCaptor<UserSubscription> subscriptionCaptor = ArgumentCaptor.forClass(UserSubscription.class);
                verify(subscriptionRepository).saveAndFlush(subscriptionCaptor.capture());

                UserSubscription cancelledSub = subscriptionCaptor.getValue();
                assertEquals(SubscriptionConstants.SubscriptionStatus.CANCELLED, cancelledSub.getStatus());
                assertNotNull(cancelledSub.getEndedAt());

                // Verify free subscription creation
                verify(subscriptionRepository, times(2)).findActiveByUserId(userId);
                verify(subscriptionRepository).save(any(UserSubscription.class));
            }

            @Test
            @DisplayName("Should handle immediate cancellation when user is already on free plan")
            void shouldHandleImmediateCancellation_WhenUserIsOnFreePlan() {
                // Given
                boolean immediate = true;
                String reason = "Test cancellation";

                UserSubscription activeFreeSubscription = UserSubscription.builder()
                        .id(UUID.randomUUID())
                        .user(user)
                        .plan(freePlan)
                        .status(SubscriptionConstants.SubscriptionStatus.ACTIVE)
                        .stripeSubscriptionId("sub_free_123")
                        .build();

                when(subscriptionRepository.findActiveByUserId(userId))
                        .thenReturn(Optional.of(activeFreeSubscription));
                when(stripeService.cancelSubscription(anyString(), anyBoolean())).thenReturn(mockStripeSubscription);
                when(subscriptionRepository.saveAndFlush(any(UserSubscription.class)))
                        .thenReturn(activeFreeSubscription);

                // When
                CancelSubscriptionData result = paymentService.cancelSubscription(user, immediate, reason);

                // Then
                assertNotNull(result);
                assertNotNull(result.cancelledSubscription());
                assertNull(result.freePlanSubscription()); // No free plan created since already on free

                verify(stripeService).cancelSubscription("sub_free_123", true);
                verify(subscriptionRepository, never()).save(any(UserSubscription.class)); // No new subscription
                                                                                           // created
            }

            @Test
            @DisplayName("Should handle race condition during immediate free subscription creation")
            void shouldHandleRaceCondition_DuringImmediateFreeSubscriptionCreation() {
                // Given
                boolean immediate = true;
                String reason = "Race condition test";

                when(subscriptionRepository.findActiveByUserId(userId))
                        .thenReturn(Optional.of(proSubscription))
                        .thenReturn(Optional.empty()); // No active after cancellation
                when(stripeService.cancelSubscription(anyString(), anyBoolean())).thenReturn(mockStripeSubscription);
                when(subscriptionRepository.saveAndFlush(any(UserSubscription.class)))
                        .thenReturn(proSubscription);
                when(planRepository.findByName(SubscriptionConstants.PlanNames.FREE))
                        .thenReturn(Optional.of(freePlan));

                // Simulate race condition: DataIntegrityViolationException during save
                when(subscriptionRepository.save(any(UserSubscription.class)))
                        .thenThrow(new DataIntegrityViolationException(
                                "duplicate key value violates unique constraint \"idx_user_active_subscription\""));

                // When
                CancelSubscriptionData result = paymentService.cancelSubscription(user, immediate, reason);

                // Then
                assertNotNull(result);
                assertNotNull(result.cancelledSubscription());
                assertNull(result.freePlanSubscription()); // No free subscription created due to race condition

                // Once in cancelSubscription, once in createFreeSubscriptionSafely
                verify(subscriptionRepository, times(2)).findActiveByUserId(userId);
                // Should attempt to save but fail
                verify(subscriptionRepository).save(any(UserSubscription.class));
            }
        }

        @Nested
        @DisplayName("Non-Immediate Cancellation")
        class NonImmediateCancellationTests {

            @Test
            @DisplayName("Should cancel Pro subscription at period end without creating free subscription immediately")
            void shouldCancelProSubscriptionAtPeriodEnd_WithoutCreatingFreeSubscriptionImmediately() {
                // Given
                boolean immediate = false;
                String reason = "Will switch to another service";

                when(subscriptionRepository.findActiveByUserId(userId))
                        .thenReturn(Optional.of(proSubscription));
                when(stripeService.cancelSubscription(anyString(), anyBoolean())).thenReturn(mockStripeSubscription);
                when(subscriptionRepository.saveAndFlush(any(UserSubscription.class)))
                        .thenReturn(proSubscription);

                // When
                CancelSubscriptionData result = paymentService.cancelSubscription(user, immediate, reason);

                // Then
                assertNotNull(result);
                assertNotNull(result.cancelledSubscription());
                assertNull(result.freePlanSubscription()); // No free plan created immediately

                // Verify Stripe cancellation (at period end)
                verify(stripeService).cancelSubscription("sub_123456789", false);

                // Verify subscription status remains ACTIVE (not changed to CANCELLED)
                ArgumentCaptor<UserSubscription> subscriptionCaptor = ArgumentCaptor.forClass(UserSubscription.class);
                verify(subscriptionRepository).saveAndFlush(subscriptionCaptor.capture());

                UserSubscription savedSubscription = subscriptionCaptor.getValue();
                assertEquals(SubscriptionConstants.SubscriptionStatus.ACTIVE, savedSubscription.getStatus());
                assertNull(savedSubscription.getEndedAt()); // Should not be ended yet

                // Verify no free subscription creation attempts
                verify(subscriptionRepository, never()).save(any(UserSubscription.class));
                verify(planRepository, never()).findByName(SubscriptionConstants.PlanNames.FREE);
            }

            @Test
            @DisplayName("Should maintain Pro access until period end for non-immediate cancellation")
            void shouldMaintainProAccess_UntilPeriodEndForNonImmediateCancellation() {
                // Given
                boolean immediate = false;
                String reason = "Cost concerns";

                when(subscriptionRepository.findActiveByUserId(userId))
                        .thenReturn(Optional.of(proSubscription));
                when(stripeService.cancelSubscription(anyString(), anyBoolean())).thenReturn(mockStripeSubscription);

                // Capture the subscription to verify it remains active
                ArgumentCaptor<UserSubscription> subscriptionCaptor = ArgumentCaptor.forClass(UserSubscription.class);
                when(subscriptionRepository.saveAndFlush(subscriptionCaptor.capture()))
                        .thenReturn(proSubscription);

                // When
                CancelSubscriptionData result = paymentService.cancelSubscription(user, immediate, reason);

                // Then
                assertNotNull(result);

                UserSubscription savedSubscription = subscriptionCaptor.getValue();

                // Verify subscription remains active with Pro benefits
                assertEquals(SubscriptionConstants.SubscriptionStatus.ACTIVE, savedSubscription.getStatus());
                assertEquals(proPlan.getName(), savedSubscription.getPlan().getName());
                assertNull(savedSubscription.getEndedAt()); // Should not be ended

                // Verify billing period is preserved
                assertNotNull(savedSubscription.getCurrentPeriodEnd());
                assertTrue(savedSubscription.getCurrentPeriodEnd().isAfter(OffsetDateTime.now()));
            }

            @ParameterizedTest
            @ValueSource(strings = { "Too expensive", "Found better alternative", "Temporary financial constraints" })
            @DisplayName("Should handle various cancellation reasons for non-immediate cancellation")
            void shouldHandleVariousCancellationReasons_ForNonImmediateCancellation(String reason) {
                // Given
                boolean immediate = false;

                when(subscriptionRepository.findActiveByUserId(userId))
                        .thenReturn(Optional.of(proSubscription));
                when(stripeService.cancelSubscription(anyString(), anyBoolean())).thenReturn(mockStripeSubscription);
                when(subscriptionRepository.saveAndFlush(any(UserSubscription.class)))
                        .thenReturn(proSubscription);

                // When
                CancelSubscriptionData result = paymentService.cancelSubscription(user, immediate, reason);

                // Then
                assertNotNull(result);
                verify(stripeService).cancelSubscription(proSubscription.getStripeSubscriptionId(), false);

                // Verify subscription status remains active
                ArgumentCaptor<UserSubscription> captor = ArgumentCaptor.forClass(UserSubscription.class);
                verify(subscriptionRepository).saveAndFlush(captor.capture());
                assertEquals(SubscriptionConstants.SubscriptionStatus.ACTIVE, captor.getValue().getStatus());
            }
        }

        @Nested
        @DisplayName("Error Scenarios")
        class ErrorScenariosTests {

            @Test
            @DisplayName("Should throw exception when no active subscription exists")
            void shouldThrowException_WhenNoActiveSubscriptionExists() {
                // Given
                when(subscriptionRepository.findActiveByUserId(userId))
                        .thenReturn(Optional.empty());

                // When & Then
                BadRequestException exception = assertThrows(BadRequestException.class,
                        () -> paymentService.cancelSubscription(user, true, "test"));

                assertEquals("No active subscription found", exception.getMessage());
                verify(stripeService, never()).cancelSubscription(anyString(), anyBoolean());
            }

            @Test
            @DisplayName("Should throw exception when subscription has no Stripe subscription ID")
            void shouldThrowException_WhenSubscriptionHasNoStripeSubscriptionId() {
                // Given
                UserSubscription subscriptionWithoutStripeId = UserSubscription.builder()
                        .id(UUID.randomUUID())
                        .user(user)
                        .plan(proPlan)
                        .status(SubscriptionConstants.SubscriptionStatus.ACTIVE)
                        .stripeSubscriptionId(null) // No Stripe ID
                        .build();

                when(subscriptionRepository.findActiveByUserId(userId))
                        .thenReturn(Optional.of(subscriptionWithoutStripeId));

                // When & Then
                BadRequestException exception = assertThrows(BadRequestException.class,
                        () -> paymentService.cancelSubscription(user, true, "test"));

                assertEquals("Cannot cancel subscription without Stripe subscription ID", exception.getMessage());
                verify(stripeService, never()).cancelSubscription(anyString(), anyBoolean());
            }

            @Test
            @DisplayName("Should handle constraint violation during free subscription creation gracefully")
            void shouldHandleConstraintViolation_DuringFreeSubscriptionCreationGracefully() {
                // Given
                boolean immediate = true;
                String reason = "Constraint test";

                when(subscriptionRepository.findActiveByUserId(userId))
                        .thenReturn(Optional.of(proSubscription))
                        .thenReturn(Optional.empty()); // No active after cancellation
                when(stripeService.cancelSubscription(anyString(), anyBoolean())).thenReturn(mockStripeSubscription);
                when(subscriptionRepository.saveAndFlush(any(UserSubscription.class)))
                        .thenReturn(proSubscription);
                when(planRepository.findByName(SubscriptionConstants.PlanNames.FREE))
                        .thenReturn(Optional.of(freePlan));

                // Simulate constraint violation during free subscription creation
                when(subscriptionRepository.save(any(UserSubscription.class)))
                        .thenThrow(new DataIntegrityViolationException(
                                "duplicate key value violates unique constraint \"idx_user_active_subscription\""));

                // When
                CancelSubscriptionData result = paymentService.cancelSubscription(user, immediate, reason);

                // Then
                assertNotNull(result);
                assertNotNull(result.cancelledSubscription());
                assertNull(result.freePlanSubscription()); // Free subscription creation failed gracefully

                verify(stripeService).cancelSubscription("sub_123456789", true);
                verify(subscriptionRepository).save(any(UserSubscription.class));
            }

            @Test
            @DisplayName("Should handle missing free plan gracefully during immediate cancellation")
            void shouldHandleMissingFreePlan_GracefullyDuringImmediateCancellation() {
                // Given
                boolean immediate = true;
                String reason = "Free plan missing test";

                when(subscriptionRepository.findActiveByUserId(userId))
                        .thenReturn(Optional.of(proSubscription))
                        .thenReturn(Optional.empty()); // No active after cancellation
                when(stripeService.cancelSubscription(anyString(), anyBoolean())).thenReturn(mockStripeSubscription);
                when(subscriptionRepository.saveAndFlush(any(UserSubscription.class)))
                        .thenReturn(proSubscription);
                when(planRepository.findByName(SubscriptionConstants.PlanNames.FREE))
                        .thenReturn(Optional.empty()); // Free plan not found

                // When
                CancelSubscriptionData result = paymentService.cancelSubscription(user, immediate, reason);

                // Then
                assertNotNull(result);
                assertNotNull(result.cancelledSubscription());
                assertNull(result.freePlanSubscription()); // Free subscription creation failed gracefully

                verify(stripeService).cancelSubscription("sub_123456789", true);
                verify(planRepository).findByName(SubscriptionConstants.PlanNames.FREE);
            }
        }
    }

    @Nested
    @DisplayName("Checkout Session Creation Tests")
    class CheckoutSessionCreationTests {

        private com.stripe.model.checkout.Session mockSession;

        @BeforeEach
        void setUp() {
            mockSession = new com.stripe.model.checkout.Session();
            mockSession.setId("cs_test_session_123");
            mockSession.setUrl("https://checkout.stripe.com/pay/cs_test_session_123");
        }

        @Test
        @DisplayName("Should create checkout session for valid Pro plan")
        void shouldCreateCheckoutSession_ForValidProPlan() {
            // Given
            when(planRepository.findByName(SubscriptionConstants.PlanNames.PRO))
                    .thenReturn(Optional.of(proPlan));
            when(subscriptionRepository.findActiveByUserId(userId))
                    .thenReturn(Optional.empty()); // No existing subscription
            when(stripeService.createCheckoutSession(user, proPlan))
                    .thenReturn(mockSession);

            // When
            com.stripe.model.checkout.Session result = paymentService.createCheckoutSession(user,
                    SubscriptionConstants.PlanNames.PRO);

            // Then
            assertNotNull(result);
            assertEquals("cs_test_session_123", result.getId());
            assertEquals("https://checkout.stripe.com/pay/cs_test_session_123", result.getUrl());

            verify(planRepository).findByName(SubscriptionConstants.PlanNames.PRO);
            verify(subscriptionRepository).findActiveByUserId(userId);
            verify(stripeService).createCheckoutSession(user, proPlan);
        }

        @Test
        @DisplayName("Should create checkout session for Free plan")
        void shouldCreateCheckoutSession_ForFreePlan() {
            // Given
            when(planRepository.findByName(SubscriptionConstants.PlanNames.FREE))
                    .thenReturn(Optional.of(freePlan));
            when(subscriptionRepository.findActiveByUserId(userId))
                    .thenReturn(Optional.empty());
            when(stripeService.createCheckoutSession(user, freePlan))
                    .thenReturn(mockSession);

            // When
            com.stripe.model.checkout.Session result = paymentService.createCheckoutSession(user,
                    SubscriptionConstants.PlanNames.FREE);

            // Then
            assertNotNull(result);
            verify(stripeService).createCheckoutSession(user, freePlan);
        }

        @Test
        @DisplayName("Should throw exception when plan not found")
        void shouldThrowException_WhenPlanNotFound() {
            // Given
            String invalidPlanName = "INVALID_PLAN";
            when(planRepository.findByName(invalidPlanName))
                    .thenReturn(Optional.empty());

            // When & Then
            ResourceNotFoundException exception = assertThrows(ResourceNotFoundException.class,
                    () -> paymentService.createCheckoutSession(user, invalidPlanName));

            assertEquals("Plan not found: " + invalidPlanName, exception.getMessage());
            verify(planRepository).findByName(invalidPlanName);
            verify(stripeService, never()).createCheckoutSession(any(), any());
        }

        @Test
        @DisplayName("Should throw exception when user already has active subscription to same plan")
        void shouldThrowException_WhenUserAlreadyHasActiveSubscriptionToSamePlan() {
            // Given
            when(planRepository.findByName(SubscriptionConstants.PlanNames.PRO))
                    .thenReturn(Optional.of(proPlan));
            when(subscriptionRepository.findActiveByUserId(userId))
                    .thenReturn(Optional.of(proSubscription)); // Already has Pro subscription

            // When & Then
            BadRequestException exception = assertThrows(BadRequestException.class,
                    () -> paymentService.createCheckoutSession(user, SubscriptionConstants.PlanNames.PRO));

            assertEquals("You already have an active subscription to this plan", exception.getMessage());
            verify(stripeService, never()).createCheckoutSession(any(), any());
        }

        @Test
        @DisplayName("Should allow checkout session when user has different active plan")
        void shouldAllowCheckoutSession_WhenUserHasDifferentActivePlan() {
            // Given - User has Free plan, wants to upgrade to Pro
            UserSubscription activeFreeSubscription = UserSubscription.builder()
                    .id(UUID.randomUUID())
                    .user(user)
                    .plan(freePlan)
                    .status(SubscriptionConstants.SubscriptionStatus.ACTIVE)
                    .build();

            when(planRepository.findByName(SubscriptionConstants.PlanNames.PRO))
                    .thenReturn(Optional.of(proPlan));
            when(subscriptionRepository.findActiveByUserId(userId))
                    .thenReturn(Optional.of(activeFreeSubscription));
            when(stripeService.createCheckoutSession(user, proPlan))
                    .thenReturn(mockSession);

            // When
            com.stripe.model.checkout.Session result = paymentService.createCheckoutSession(user,
                    SubscriptionConstants.PlanNames.PRO);

            // Then
            assertNotNull(result);
            verify(stripeService).createCheckoutSession(user, proPlan);
        }
    }

    @Nested
    @DisplayName("Checkout Success Processing Tests")
    class CheckoutSuccessProcessingTests {

        private com.stripe.model.checkout.Session mockStripeSession;
        private final String sessionId = "cs_test_session_success_123";

        @BeforeEach
        void setUp() {
            mockStripeSession = new com.stripe.model.checkout.Session();
            mockStripeSession.setId(sessionId);
            mockStripeSession.setSubscription("sub_test_subscription_123");

            // Create metadata map
            java.util.Map<String, String> metadata = new java.util.HashMap<>();
            metadata.put("user_id", userId.toString());
            mockStripeSession.setMetadata(metadata);
        }

        @Test
        @DisplayName("Should process checkout success for valid session")
        void shouldProcessCheckoutSuccess_ForValidSession() throws Exception {
            // Given
            String stripeSubscriptionId = "sub_test_subscription_123";

            // Mock static Stripe session retrieval
            try (MockedStatic<com.stripe.model.checkout.Session> mockedSession = org.mockito.Mockito
                    .mockStatic(com.stripe.model.checkout.Session.class)) {
                mockedSession.when(() -> com.stripe.model.checkout.Session.retrieve(sessionId))
                        .thenReturn(mockStripeSession);

                when(subscriptionRepository.findByStripeSubscriptionId(stripeSubscriptionId))
                        .thenReturn(Optional.of(proSubscription));

                // When
                PaymentService.CheckoutSuccessData result = paymentService.processCheckoutSuccess(user, sessionId);

                // Then
                assertNotNull(result);
                assertNotNull(result.session());
                assertNotNull(result.subscription());
                assertEquals(sessionId, result.session().getId());
                assertEquals(proSubscription.getId(), result.subscription().getId());

                verify(subscriptionRepository).findByStripeSubscriptionId(stripeSubscriptionId);
            }
        }

        @Test
        @DisplayName("Should throw exception when session belongs to different user")
        void shouldThrowException_WhenSessionBelongsToDifferentUser() throws Exception {
            // Given
            java.util.Map<String, String> wrongMetadata = new java.util.HashMap<>();
            wrongMetadata.put("user_id", UUID.randomUUID().toString()); // Different user ID
            mockStripeSession.setMetadata(wrongMetadata);

            try (MockedStatic<com.stripe.model.checkout.Session> mockedSession = org.mockito.Mockito
                    .mockStatic(com.stripe.model.checkout.Session.class)) {
                mockedSession.when(() -> com.stripe.model.checkout.Session.retrieve(sessionId))
                        .thenReturn(mockStripeSession);

                // When & Then
                BadRequestException exception = assertThrows(BadRequestException.class,
                        () -> paymentService.processCheckoutSuccess(user, sessionId));

                assertEquals("Invalid session for this user", exception.getMessage());
                verify(subscriptionRepository, never()).findByStripeSubscriptionId(anyString());
            }
        }

        @Test
        @DisplayName("Should throw exception when session has no subscription")
        void shouldThrowException_WhenSessionHasNoSubscription() throws Exception {
            // Given
            mockStripeSession.setSubscription(null); // No subscription

            try (MockedStatic<com.stripe.model.checkout.Session> mockedSession = org.mockito.Mockito
                    .mockStatic(com.stripe.model.checkout.Session.class)) {
                mockedSession.when(() -> com.stripe.model.checkout.Session.retrieve(sessionId))
                        .thenReturn(mockStripeSession);

                // When & Then
                BadRequestException exception = assertThrows(BadRequestException.class,
                        () -> paymentService.processCheckoutSuccess(user, sessionId));

                assertEquals("No subscription found in checkout session", exception.getMessage());
                verify(subscriptionRepository, never()).findByStripeSubscriptionId(anyString());
            }
        }

        @Test
        @DisplayName("Should throw exception when subscription not found in database")
        void shouldThrowException_WhenSubscriptionNotFoundInDatabase() throws Exception {
            // Given
            String stripeSubscriptionId = "sub_test_subscription_123";

            try (MockedStatic<com.stripe.model.checkout.Session> mockedSession = org.mockito.Mockito
                    .mockStatic(com.stripe.model.checkout.Session.class)) {
                mockedSession.when(() -> com.stripe.model.checkout.Session.retrieve(sessionId))
                        .thenReturn(mockStripeSession);

                when(subscriptionRepository.findByStripeSubscriptionId(stripeSubscriptionId))
                        .thenReturn(Optional.empty()); // Subscription not found

                // When & Then
                ResourceNotFoundException exception = assertThrows(ResourceNotFoundException.class,
                        () -> paymentService.processCheckoutSuccess(user, sessionId));

                assertEquals("Subscription not found", exception.getMessage());
                verify(subscriptionRepository).findByStripeSubscriptionId(stripeSubscriptionId);
            }
        }

        @Test
        @DisplayName("Should throw exception when Stripe session retrieval fails")
        void shouldThrowException_WhenStripeSessionRetrievalFails() throws Exception {
            // Given
            try (MockedStatic<com.stripe.model.checkout.Session> mockedSession = org.mockito.Mockito
                    .mockStatic(com.stripe.model.checkout.Session.class)) {
                mockedSession.when(() -> com.stripe.model.checkout.Session.retrieve(sessionId))
                        .thenThrow(new com.stripe.exception.StripeException("Session not found", "req_123",
                                "session_not_found", 404) {
                        });

                // When & Then
                BadRequestException exception = assertThrows(BadRequestException.class,
                        () -> paymentService.processCheckoutSuccess(user, sessionId));

                assertTrue(exception.getMessage().startsWith("Failed to verify checkout:"));
                verify(subscriptionRepository, never()).findByStripeSubscriptionId(anyString());
            }
        }
    }

    @Nested
    @DisplayName("Plan Synchronization Tests")
    class PlanSynchronizationTests {

        private StripeService.StripeResourceIds mockStripeIds;

        @BeforeEach
        void setUp() {
            mockStripeIds = new StripeService.StripeResourceIds("prod_test_123", "price_test_123");
        }

        @Test
        @DisplayName("Should sync Pro plan with Stripe successfully")
        void shouldSyncProPlan_WithStripeSuccessfully() {
            // Given
            when(planRepository.findByName(SubscriptionConstants.PlanNames.PRO))
                    .thenReturn(Optional.of(proPlan));
            when(stripeService.syncPlanWithStripe(proPlan))
                    .thenReturn(mockStripeIds);
            when(planRepository.save(any(SubscriptionPlan.class)))
                    .thenReturn(proPlan);

            // When
            paymentService.syncPlanWithStripe(SubscriptionConstants.PlanNames.PRO);

            // Then
            verify(planRepository).findByName(SubscriptionConstants.PlanNames.PRO);
            verify(stripeService).syncPlanWithStripe(proPlan);

            // Verify plan was updated with Stripe IDs
            ArgumentCaptor<SubscriptionPlan> planCaptor = ArgumentCaptor.forClass(SubscriptionPlan.class);
            verify(planRepository).save(planCaptor.capture());

            SubscriptionPlan savedPlan = planCaptor.getValue();
            assertEquals("prod_test_123", savedPlan.getStripeProductId());
            assertEquals("price_test_123", savedPlan.getStripePriceId());
        }

        @Test
        @DisplayName("Should sync Free plan with Stripe without price ID")
        void shouldSyncFreePlan_WithStripeWithoutPriceId() {
            // Given
            StripeService.StripeResourceIds freeStripeIds = new StripeService.StripeResourceIds("prod_free_123", null);

            when(planRepository.findByName(SubscriptionConstants.PlanNames.FREE))
                    .thenReturn(Optional.of(freePlan));
            when(stripeService.syncPlanWithStripe(freePlan))
                    .thenReturn(freeStripeIds);
            when(planRepository.save(any(SubscriptionPlan.class)))
                    .thenReturn(freePlan);

            // When
            paymentService.syncPlanWithStripe(SubscriptionConstants.PlanNames.FREE);

            // Then
            verify(stripeService).syncPlanWithStripe(freePlan);

            ArgumentCaptor<SubscriptionPlan> planCaptor = ArgumentCaptor.forClass(SubscriptionPlan.class);
            verify(planRepository).save(planCaptor.capture());

            SubscriptionPlan savedPlan = planCaptor.getValue();
            assertEquals("prod_free_123", savedPlan.getStripeProductId());
            // Price ID should not be updated for free plan (remains null or unchanged)
        }

        @Test
        @DisplayName("Should throw exception when plan not found for synchronization")
        void shouldThrowException_WhenPlanNotFoundForSynchronization() {
            // Given
            String invalidPlanName = "INVALID_PLAN";
            when(planRepository.findByName(invalidPlanName))
                    .thenReturn(Optional.empty());

            // When & Then
            ResourceNotFoundException exception = assertThrows(ResourceNotFoundException.class,
                    () -> paymentService.syncPlanWithStripe(invalidPlanName));

            assertEquals("Plan not found: " + invalidPlanName, exception.getMessage());
            verify(stripeService, never()).syncPlanWithStripe(any());
            verify(planRepository, never()).save(any());
        }

        @ParameterizedTest
        @ValueSource(strings = { SubscriptionConstants.PlanNames.PRO, SubscriptionConstants.PlanNames.FREE })
        @DisplayName("Should handle various plan types for synchronization")
        void shouldHandleVariousPlanTypes_ForSynchronization(String planName) {
            // Given
            SubscriptionPlan plan = planName.equals(SubscriptionConstants.PlanNames.PRO) ? proPlan : freePlan;
            String expectedPriceId = planName.equals(SubscriptionConstants.PlanNames.PRO) ? "price_test_123" : null;
            StripeService.StripeResourceIds stripeIds = new StripeService.StripeResourceIds("prod_test_123",
                    expectedPriceId);

            when(planRepository.findByName(planName))
                    .thenReturn(Optional.of(plan));
            when(stripeService.syncPlanWithStripe(plan))
                    .thenReturn(stripeIds);
            when(planRepository.save(any(SubscriptionPlan.class)))
                    .thenReturn(plan);

            // When
            paymentService.syncPlanWithStripe(planName);

            // Then
            verify(stripeService).syncPlanWithStripe(plan);
            verify(planRepository).save(any(SubscriptionPlan.class));
        }

        @Test
        @DisplayName("Should update existing Stripe IDs when syncing plan")
        void shouldUpdateExistingStripeIds_WhenSyncingPlan() {
            // Given
            SubscriptionPlan existingPlan = SubscriptionPlan.builder()
                    .id(proPlan.getId())
                    .name(proPlan.getName())
                    .displayName(proPlan.getDisplayName())
                    .maxOrganizations(proPlan.getMaxOrganizations())
                    .priceMonthlyCents(proPlan.getPriceMonthlyCents())
                    .stripeProductId("old_prod_123")
                    .stripePriceId("old_price_123")
                    .isActive(true)
                    .build();

            when(planRepository.findByName(SubscriptionConstants.PlanNames.PRO))
                    .thenReturn(Optional.of(existingPlan));
            when(stripeService.syncPlanWithStripe(existingPlan))
                    .thenReturn(mockStripeIds);
            when(planRepository.save(any(SubscriptionPlan.class)))
                    .thenReturn(existingPlan);

            // When
            paymentService.syncPlanWithStripe(SubscriptionConstants.PlanNames.PRO);

            // Then
            ArgumentCaptor<SubscriptionPlan> planCaptor = ArgumentCaptor.forClass(SubscriptionPlan.class);
            verify(planRepository).save(planCaptor.capture());

            SubscriptionPlan savedPlan = planCaptor.getValue();
            assertEquals("prod_test_123", savedPlan.getStripeProductId());
            assertEquals("price_test_123", savedPlan.getStripePriceId());
        }
    }
}
